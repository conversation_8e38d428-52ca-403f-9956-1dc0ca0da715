module "neo4j" {
  source = "../../../../../infrastructure/modules/instance/neo4j"

  project_id             = local.project_id
  common_resource_labels = local.common_resource_labels

  neo4j_customers = [{
    database_name = "superior_uniform_eudoraar"
    tenant_name   = "superior_uniform"
    facility_name = "superioruniform_eudoraar"
    },
    {
      database_name = "acehardware_wilmertx"
      tenant_name   = "acehardware"
      facility_name = "acehardware_wilmertx"
    },

  ]
}

