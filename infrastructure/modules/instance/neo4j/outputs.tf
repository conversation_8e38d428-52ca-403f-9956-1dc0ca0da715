output "neo4j_instances" {
  description = "Details of newly created Neo4j AuraDB instances only"
  value = {
    for name, info in local.neo4j_created_map : name => {
      instance_id   = info.instance_id
      instance_name = info.database_name
      uri           = "neo4j+s://${info.instance_id}.databases.neo4j.io"
      secret_id     = google_secret_manager_secret.neo4j_instances[name].secret_id
      status        = "newly_created"
    }
  }

  depends_on = [data.external.neo4j_created]
}

output "all_secrets" {
  description = "List of GCP Secret Manager secret IDs for all Neo4j instances (containers only)"
  value       = [for customer in var.neo4j_customers : google_secret_manager_secret.neo4j_instances[customer.database_name].secret_id]
}

output "created_secrets" {
  description = "List of GCP Secret Manager secret IDs for newly created Neo4j instances (with data)"
  value       = [for name in keys(local.neo4j_created_map) : google_secret_manager_secret.neo4j_instances[name].secret_id]
}

output "auth_status" {
  description = "Status of the Neo4j Aura API authentication"
  value = {
    status_code = data.http.aura_token.status_code
    success     = data.http.aura_token.status_code == 200
  }
}
