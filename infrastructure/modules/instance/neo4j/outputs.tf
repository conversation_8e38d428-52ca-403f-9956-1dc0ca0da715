output "neo4j_instances" {
  description = "Details of created Neo4j AuraDB instances"
  value = {
    for name, info in local.neo4j_created_map : name => {
      instance_id   = info.instance_id
      instance_name = info.database_name
      uri           = "neo4j+s://${info.instance_id}.databases.neo4j.io"
      secret_id     = google_secret_manager_secret.neo4j_instances_created[name].secret_id
      status        = "created"
    }
  }

  depends_on = [data.external.neo4j_created]
}

output "created_secrets" {
  description = "List of GCP Secret Manager secret IDs created for Neo4j instances (only newly created)"
  value       = [for name in keys(local.neo4j_created_map) : google_secret_manager_secret.neo4j_instances_created[name].secret_id]
}

output "auth_status" {
  description = "Status of the Neo4j Aura API authentication"
  value = {
    status_code = data.http.aura_token.status_code
    success     = data.http.aura_token.status_code == 200
  }
}
