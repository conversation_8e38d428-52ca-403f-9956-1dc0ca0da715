output "neo4j_instances" {
  description = "Details of Neo4j AuraDB instances (shows status for all requested instances)"
  value = {
    for customer in var.neo4j_customers : customer.database_name => {
      instance_id   = lookup(local.neo4j_created_map, customer.database_name, null) != null ? local.neo4j_created_map[customer.database_name].instance_id : "existing_or_not_created"
      instance_name = customer.database_name
      uri           = lookup(local.neo4j_created_map, customer.database_name, null) != null ? "neo4j+s://${local.neo4j_created_map[customer.database_name].instance_id}.databases.neo4j.io" : "existing_or_not_created"
      secret_id     = google_secret_manager_secret.neo4j_instances[customer.database_name].secret_id
      status        = contains(local.instances_to_create, customer.database_name) ? (lookup(local.neo4j_created_map, customer.database_name, null) != null ? "newly_created" : "creation_pending") : "existing"
    }
  }

  depends_on = [data.external.neo4j_created]
}

output "all_secrets" {
  description = "List of GCP Secret Manager secret IDs for all Neo4j instances (containers only)"
  value       = [for customer in var.neo4j_customers : google_secret_manager_secret.neo4j_instances[customer.database_name].secret_id]
}

output "created_secrets" {
  description = "List of GCP Secret Manager secret IDs for newly created Neo4j instances (with data)"
  value       = [for name in keys(local.neo4j_created_map) : google_secret_manager_secret.neo4j_instances[name].secret_id]
}

output "auth_status" {
  description = "Status of the Neo4j Aura API authentication"
  value = {
    status_code = data.http.aura_token.status_code
    success     = data.http.aura_token.status_code == 200
  }
}
