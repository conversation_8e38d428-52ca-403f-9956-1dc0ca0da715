#!/usr/bin/env python3
"""
Neo4j AuraDB management helper for Terraform.

This script is invoked in three different scenarios:
1. Plan: Determine which Neo4j instances need to be created
2. Apply: Create the specified Neo4j instances and collect credentials
3. Read: Retrieve previously created instance data from file

Inputs are provided via stdin as JSON, and outputs are written to stdout as JSON.
"""

import json
import sys
import os
import hashlib
import urllib.request
import urllib.parse
import urllib.error


def do_plan(inputs) -> dict:
    """
    Plan mode: Determine which Neo4j instances need to be created.

    Inputs (stdin JSON):
    {
      "action": "plan",
      "api_base_url": "https://api.neo4j.io/v1",
      "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6...",
      "aura_tenant_id": "aura_tenant_id",
      "neo4j_customers": "[{\"database_name\":\"customer1\",...}]"
    }
    Behavior: GET /instances?tenantId=AURA_TENANT_ID, compute which database_names are missing
    Output: { "to_create_json": "[\"customer1\"]", "to_create_hash": "4f53cda18c2baa0c" }
    """
    # Validate required inputs
    required_fields = [
        "api_base_url",
        "access_token",
        "aura_tenant_id",
        "neo4j_customers",
    ]
    missing = [field for field in required_fields if not inputs.get(field)]
    if missing:
        raise ValueError(f"Missing required inputs for plan: {', '.join(missing)}")

    api_base_url = inputs["api_base_url"]
    access_token = inputs["access_token"]
    aura_tenant_id = inputs["aura_tenant_id"]
    neo4j_customers = json.loads(inputs["neo4j_customers"])

    # Get existing instances
    tenant_query = urllib.parse.urlencode({"tenantId": aura_tenant_id})
    url = f"{api_base_url}/instances?{tenant_query}"

    request = urllib.request.Request(
        url,
        headers={
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
        },
    )

    with urllib.request.urlopen(request, timeout=30) as response:
        if response.status != 200:
            raise urllib.error.HTTPError(
                url,
                response.status,
                f"GET request failed with status {response.status}",
                response.headers,
                None,
            )
        response_data = json.loads(response.read().decode())

    # Parse existing instance names
    # Example response:
    # {
    #   "data": [
    #     {
    #       "name": "acehardware_wilmertx",
    #       "id": "1234567890",
    #       "username": "neo4j",
    #       "password": "secure-password"
    #     }
    #   ]
    # }
    existing_instances = response_data.get("data", [])

    # Determine which instances need to be created
    desired_names = {customer["database_name"] for customer in neo4j_customers}

    # Create complete desired state map
    all_instances_map = {}

    # Add existing instances (preserve their data)
    for instance in existing_instances:
        if instance["name"] in desired_names:
            all_instances_map[instance["name"]] = {
                "instance_id": instance["id"],
                "database_name": instance["name"],
                "username": instance.get("username", "neo4j"),
                "password": "EXISTING_PASSWORD_PRESERVED",
            }

    # Add placeholders for missing instances (will be filled during apply)
    for name in desired_names:
        if name not in all_instances_map:
            all_instances_map[name] = {
                "instance_id": "TO_BE_CREATED",
                "database_name": name,
                "username": "TO_BE_CREATED",
                "password": "TO_BE_CREATED",
            }

    # Generate hash for the complete desired state
    all_instances_json = json.dumps(all_instances_map, sort_keys=True)
    state_hash = hashlib.sha256(all_instances_json.encode()).hexdigest()[:16]

    return {
        "all_instances_json": all_instances_json,
        "all_instances_hash": state_hash,
    }


def do_apply(inputs) -> dict:
    """
    Apply mode: Create the specified Neo4j instances and collect credentials.

    Inputs (stdin JSON):
    {
      "action": "apply",
      "api_base_url": "https://api.neo4j.io/v1",
      "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6...",
      "aura_tenant_id": "aura_tenant_id",
      "neo4j_version": "5",
      "aura_region": "us-east1",
      "instance_memory": "1GB",
      "instance_storage": "2GB",
      "aura_type": "professional-db",
      "to_create": ["tenant_1", "tenant_2"],
      "results_path": "/path/to/.neo4j_created.json"
    }
    Behavior: For each in to_create, POST /instances and collect credentials
    Side effect: writes results_path JSON with a map keyed by database_name containing
                 instance_id, username, password
    Output: { "status": "ok", "created_count": "2" }
    """
    # Validate required inputs
    required_fields = [
        "api_base_url",
        "access_token",
        "aura_tenant_id",
        "to_create",
        "results_path",
        "neo4j_version",
        "aura_region",
        "instance_memory",
        "instance_storage",
        "aura_type",
    ]
    missing = [field for field in required_fields if not inputs.get(field)]
    if missing:
        raise ValueError(f"Missing required inputs for apply: {', '.join(missing)}")

    api_base_url = inputs["api_base_url"]
    access_token = inputs["access_token"]
    aura_tenant_id = inputs["aura_tenant_id"]
    to_create = inputs["to_create"]
    results_path = inputs["results_path"]

    # Instance configuration
    config = {
        "version": inputs["neo4j_version"],
        "region": inputs["aura_region"],
        "memory": inputs["instance_memory"],
        "storage": inputs["instance_storage"],
        "cloud_provider": "gcp",
        "type": inputs["aura_type"],
        "tenant_id": aura_tenant_id,
    }

    created_instances = {}

    # Create each instance
    for database_name in to_create:
        instance_data = {**config, "name": database_name}

        request_data = json.dumps(instance_data).encode()
        request = urllib.request.Request(
            f"{api_base_url}/instances",
            data=request_data,
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
                "Accept": "application/json",
            },
        )

        try:
            with urllib.request.urlopen(request, timeout=60) as response:
                response_body = response.read().decode()
                if response.status != 202:
                    print(
                        f"DEBUG: API Error - Status: {response.status}", file=sys.stderr
                    )
                    print(
                        f"DEBUG: API Error - Response: {response_body}", file=sys.stderr
                    )
                    raise urllib.error.HTTPError(
                        response.url,
                        response.status,
                        f"POST request failed with status {response.status}: {response_body}",
                        response.headers,
                        None,
                    )
                try:
                    response_data = json.loads(response_body)
                except json.JSONDecodeError as e:
                    print(f"DEBUG: JSON decode error: {e}", file=sys.stderr)
                    print(f"DEBUG: Raw response body: {response_body}", file=sys.stderr)
                    raise ValueError(f"API returned invalid JSON: {e}") from e

                print(
                    f"DEBUG: API Success - Status: {response.status}", file=sys.stderr
                )
                print(
                    f"DEBUG: API Success - Response: {response_body}", file=sys.stderr
                )
        except urllib.error.HTTPError as e:
            print(f"DEBUG: HTTP Error for {database_name}: {e}", file=sys.stderr)
            raise
        except Exception as e:
            print(f"DEBUG: Request failed for {database_name}: {e}", file=sys.stderr)
            raise

        # Extract credentials from response
        data = response_data.get("data", {})
        created_instances[database_name] = {
            "instance_id": data.get("id"),
            "database_name": database_name,
            "username": data.get("username"),
            "password": data.get("password"),
        }

    # Save results to file
    with open(results_path, "w", encoding="utf-8") as f:
        json.dump(created_instances, f, indent=2)

    return {"status": "ok", "created_count": str(len(created_instances))}


def do_read_created(inputs) -> dict:
    """
    Read mode: Retrieve previously created instance data from file.

    Inputs (stdin JSON):
    {
      "action": "read_created",
      "results_path": "/path/to/.neo4j_created.json"
    }
    Output: { "created_map_json": "{\"acehardware_wilmertx\":{\"instance_id\":\"abc123\",
              \"database_name\":\"acehardware_wilmertx\",\"username\":\"neo4j\",
              \"password\":\"secure-password\"}}" }
    """
    print("DEBUG: do_read_created called", file=sys.stderr)
    results_path = inputs.get("results_path", "/tmp/neo4j_created.json")
    print(f"DEBUG: Reading from results_path: {results_path}", file=sys.stderr)

    if os.path.exists(results_path):
        print(f"DEBUG: Results file exists: {results_path}", file=sys.stderr)
        with open(results_path, "r", encoding="utf-8") as f:
            content = f.read()
            print(f"DEBUG: File content: {content}", file=sys.stderr)
            created_map = json.loads(content) if content else {}
    else:
        print(f"DEBUG: Results file does not exist: {results_path}", file=sys.stderr)
        created_map = {}

    print(f"DEBUG: Returning created_map: {created_map}", file=sys.stderr)
    return {"created_map_json": json.dumps(created_map)}


def main() -> None:
    """Main entry point for the script."""
    try:
        inputs = json.load(sys.stdin)
        action = inputs.get("action", "plan")

        if action == "plan":
            result = do_plan(inputs)
        elif action == "apply":
            result = do_apply(inputs)
        elif action == "read_created":
            result = do_read_created(inputs)
        else:
            raise ValueError(f"Unknown action: {action}")

        json.dump(result, sys.stdout)

    except urllib.error.HTTPError as e:
        json.dump({"error": f"HTTP request failed: {e}"}, sys.stdout)
        sys.exit(1)
    except urllib.error.URLError as e:
        json.dump({"error": f"API request failed: {e}"}, sys.stdout)
        sys.exit(1)
    except (json.JSONDecodeError, ValueError, KeyError, OSError) as e:
        json.dump({"error": f"Operation failed: {e}"}, sys.stdout)
        sys.exit(1)


if __name__ == "__main__":

    main()
