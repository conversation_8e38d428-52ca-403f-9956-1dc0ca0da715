#!/bin/bash

# Create Environment Script
# Usage: ./scripts/create-environment.sh <source_env> <new_env_name> <instance_name> <instance_friendly_name>
# Example: ./scripts/create-environment.sh stage ben_test bentest BenTest
#
# This script creates a new environment by copying an existing one and updating all configuration.
# 
# SOURCE ENVIRONMENT CHOICES:
# - "dev": Development environment with simulation API keys and dev-specific configs
# - "stage": Staging environment, more production-like, cleaner for testing
# - "prod": Production environment (use with caution!)
#
# WHAT THIS CREATES:
# - A new GCP project (e.g., ict-np-us1-bentest-abc123)
# - All infrastructure resources (Cloud Run, databases, load balancers, etc.)
# - Custom IP ranges to avoid conflicts
# - Proper naming and configuration

set -e

# Check if we have the right number of arguments
if [ $# -ne 4 ]; then
    echo "Usage: $0 <source_env> <new_env_name> <instance_name> <instance_friendly_name>"
    echo "Example: $0 stage ben_test bentest BenTest"
    echo ""
    echo "SOURCE ENVIRONMENT CHOICES:"
    echo "  dev   - Development environment (has simulation API keys)"
    echo "  stage - Staging environment (production-like, recommended for testing)"
    echo "  prod  - Production environment (use with caution!)"
    echo ""
    echo "This will:"
    echo "1. Copy environments/dematicnonprod/<source_env> to environments/dematicnonprod/<new_env_name>"
    echo "2. Update all configuration files with the new names"
    echo "3. Rename the instance folder to match the new naming convention"
    echo "4. Add custom IP ranges for the new environment"
    echo "5. Create a new GCP project when you run terragrunt apply"
    exit 1
fi

SOURCE_ENV=$1
NEW_ENV=$2
INSTANCE_NAME=$3
INSTANCE_FRIENDLY_NAME=$4

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Creating new environment: ${NEW_ENV} from ${SOURCE_ENV}${NC}"
echo ""

# Check if source environment exists
if [ ! -d "environments/dematicnonprod/${SOURCE_ENV}" ]; then
    echo -e "${RED}❌ Source environment '${SOURCE_ENV}' not found!${NC}"
    exit 1
fi

# Check if target environment already exists
if [ -d "environments/dematicnonprod/${NEW_ENV}" ]; then
    echo -e "${RED}❌ Target environment '${NEW_ENV}' already exists!${NC}"
    exit 1
fi

# Step 1: Copy the environment folder
echo -e "${YELLOW}📁 Copying environment structure...${NC}"
cp -r "environments/dematicnonprod/${SOURCE_ENV}" "environments/dematicnonprod/${NEW_ENV}"

# Step 2: Find and rename the instance folder
echo -e "${YELLOW}🔄 Renaming instance folder...${NC}"
cd "environments/dematicnonprod/${NEW_ENV}"

# Find the instance folder (should be something like ict-np-us1-dev)
INSTANCE_FOLDER=$(find . -maxdepth 1 -type d -name "ict-np-us1-*" | head -1)
if [ -z "$INSTANCE_FOLDER" ]; then
    echo -e "${RED}❌ Could not find instance folder in ${NEW_ENV}${NC}"
    exit 1
fi

# Extract the base name and create new name
BASE_NAME=$(echo "$INSTANCE_FOLDER" | sed 's|^\./||')
NEW_INSTANCE_FOLDER="ict-np-us1-${INSTANCE_NAME}"
mv "$INSTANCE_FOLDER" "$NEW_INSTANCE_FOLDER"

echo "  Renamed: ${BASE_NAME} → ${NEW_INSTANCE_FOLDER}"

# Step 3: Update environment.hcl
echo -e "${YELLOW}📝 Updating environment.hcl...${NC}"
sed -i.bak \
    -e "s/# Dev Environment Variables/# ${INSTANCE_FRIENDLY_NAME} Environment Variables/" \
    -e "s/environment_folder_name   = \"${SOURCE_ENV}\"/environment_folder_name   = \"${NEW_ENV}\"/" \
    -e "s/environment_friendly_name = \"Dev\"/environment_friendly_name = \"${INSTANCE_FRIENDLY_NAME}\"/" \
    -e "s/environment_name          = \"${SOURCE_ENV}\"/environment_name          = \"${NEW_ENV}\"/" \
    -e "s/#DematicNonProd\/ControlTower\/${SOURCE_ENV}/#DematicNonProd\/ControlTower\/${NEW_ENV}/" \
    environment.hcl

# Step 4: Update instance.hcl
echo -e "${YELLOW}📝 Updating instance.hcl...${NC}"
sed -i.bak \
    -e "s/# US1 Dev Instance Variables/# US1 ${INSTANCE_FRIENDLY_NAME} Instance Variables/" \
    -e "s/instance_name          = \"us1\"/instance_name          = \"${INSTANCE_NAME}\"/" \
    -e "s/instance_friendly_name = \"US1\"/instance_friendly_name = \"${INSTANCE_FRIENDLY_NAME}\"/" \
    -e "s/use_environment_name_for_instance_name = true/use_environment_name_for_instance_name = false/" \
    "${NEW_INSTANCE_FOLDER}/instance.hcl"

# Step 5: Update README.md
echo -e "${YELLOW}📝 Updating README.md...${NC}"
sed -i.bak \
    -e "s/# Dev/# ${INSTANCE_FRIENDLY_NAME}/" \
    -e "s/\"${SOURCE_ENV}\" environment/\"${NEW_ENV}\" environment/" \
    -e "s/DematicNonProd\/ControlTower\/${SOURCE_ENV}/DematicNonProd\/ControlTower\/${NEW_ENV}/" \
    -e "s/dev instances/${NEW_ENV} instances/" \
    -e "s/when we talk about \"${SOURCE_ENV}\"/when we talk about \"${NEW_ENV}\"/" \
    -e "s/ict-np-us1-${SOURCE_ENV}/ict-np-us1-${INSTANCE_NAME}/" \
    README.md

# Step 6: Add custom IP ranges to resources terragrunt.hcl
echo -e "${YELLOW}🌐 Adding custom IP ranges...${NC}"
RESOURCES_TF="${NEW_INSTANCE_FOLDER}/resources/terragrunt.hcl"

# Create a temporary file with the IP ranges configuration
cat > /tmp/ip_ranges.tf << EOF
# Override global IP ranges for ${NEW_ENV} environment
inputs = {
  global_default_ip_ranges = {
    api                     = "**********/28"
    ignition_proxy          = "**********/24"
    metric_processor        = "**********/28"
    private_service_connect = "***********"
    typeorm_migrations      = "**********/28"
  }
}

EOF

# Insert the IP ranges after all the include blocks but before the generate block
awk '/^generate "variables" {/ { system("cat /tmp/ip_ranges.tf"); print; next } 1' "$RESOURCES_TF" > "${RESOURCES_TF}.tmp" && mv "${RESOURCES_TF}.tmp" "$RESOURCES_TF"

# Clean up temp file
rm -f /tmp/ip_ranges.tf



# Clean up backup files
echo -e "${YELLOW}🧹 Cleaning up backup files...${NC}"
find . -name "*.bak" -delete

# Go back to root
cd ../../../..

echo ""
echo -e "${GREEN}✅ Environment '${NEW_ENV}' created successfully!${NC}"
echo ""
echo -e "${YELLOW}📋 Summary:${NC}"
echo "  • Source: environments/dematicnonprod/${SOURCE_ENV}"
echo "  • Target: environments/dematicnonprod/${NEW_ENV}"
echo "  • Instance: ${NEW_INSTANCE_FOLDER}"
echo "  • Instance Name: ${INSTANCE_NAME}"
echo "  • Friendly Name: ${INSTANCE_FRIENDLY_NAME}"
echo ""
echo -e "${YELLOW}🚀 Next steps (IMPORTANT - follow this order):${NC}"
echo "  1. Review the configuration files"
echo "  2. Create the project FIRST:"
echo "     cd environments/dematicnonprod/${NEW_ENV}/${NEW_INSTANCE_FOLDER}/project"
echo "     terragrunt init -reconfigure"
echo "     terragrunt plan"
echo "     terragrunt apply"
echo "  3. Then create the resources:"
echo "     cd ../resources"
echo "     terragrunt init -reconfigure"
echo "     terragrunt plan"
echo "     terragrunt apply"
echo ""
echo -e "${GREEN}✅ Environment is ready to use!${NC}"
echo -e "${YELLOW}⚠️  Note: You may need to adjust IP ranges if they conflict with existing environments${NC}"
echo -e "${RED}🚨 IMPORTANT: Always create the PROJECT before RESOURCES!${NC}"
