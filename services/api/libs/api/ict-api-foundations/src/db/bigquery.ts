import {BigQuery, Query, QueryRowsResponse} from '@google-cloud/bigquery';
import {Database} from './database.ts';
import {DatabaseTypes} from './db-types.ts';
import {Container} from '../di/type-di.ts';
import {<PERSON><PERSON>ogger} from '../log/winston-logger.ts';
import {ContextService} from '../context/context-service.ts';
import {EnvironmentService} from '../services/environment-service.ts';

/**
 * Object that holds the actual BigQuery client object and relevant information for a connection.
 */
export class BigQueryDatabase extends Database {
  private contextService: ContextService;
  private envService: EnvironmentService;
  private logger: WinstonLogger;
  constructor(
    protected _client: BigQuery,
    protected _dataset: string,
  ) {
    super();
    this.logger = Container.get(WinstonLogger);
    this.logger.info('BigQuery database initialized', {
      dataset: _dataset,
      projectId: _client.projectId,
    });
    this.contextService = Container.get(ContextService);
    this.envService = Container.get(EnvironmentService);
  }

  /**
   * BigQuery client provided by Google.
   */
  get client(): BigQuery {
    return this._client;
  }

  /**
   * Dataset that this BigQueryDatabase is currently working in.
   */
  get dataset(): string {
    return this._dataset;
  }
  set dataset(val: string) {
    this._dataset = val;
  }

  /**
   * Returns a full table path with project id and dataset for a BigQuery table.
   * @param tableName Name of the BigQuery table to get a full path for.
   * @returns Escaped string of the full path of the specified table.
   */
  public getFullTablePath(tableName: string): string {
    // Side Effect - add any table name lookup to HTTP context. This will allow us to provide
    // metadata to the API caller as to what underlying tables are used
    this.contextService.addTableName(tableName);

    return String.raw`${this.client.projectId}.${this.dataset}.${tableName}`;
  }
  /**
   * Returns a full table path with the EDP project id and dataset for a BigQuery table.
   * @param tableName Name of the BigQuery table to get a full path for.
   * @returns Escaped string of the full path of the specified table.
   */
  public getEdpFullTablePath(tableName: string): string {
    // Side Effect - add any table name lookup to HTTP context. This will allow us to provide
    // metadata to the API caller as to what underlying tables are used
    this.contextService.addTableName(`${this.dataset}.${tableName}`);

    return String.raw`${this.envService.edpBigQueryDb.projectId}.${this.dataset}.${tableName}`;
  }

  /**
   * Gets the full path, including dataset, to the function.
   * @param functionName Name of the function to get a path for.
   * @returns Path of the function.
   */
  public getFunctionPath(functionName: string): string {
    return `${this.dataset}.${functionName}`;
  }

  /**
   * Creates a job based on the BigQuery Query passed in sqlOptions, adds the necessary
   * labels to the job, then executes it and logs the relevant information about the job
   * such as bytesBilled, url, etc.
   * @param sqlOptions the query to be executed along with any other options
   * @returns the results of the BigQuery job
   */
  public async executeMonitoredJob(
    sqlOptions: Query,
  ): Promise<QueryRowsResponse> {
    // Create a copy of sqlOptions before modifying
    const options = {...sqlOptions};

    // Add labels
    options.labels = {
      ...sqlOptions.labels,
      application: 'control-tower-intelligent',
      tenant: this.contextService.datasetId,
    };

    const [job] = await this.client.createQueryJob(options);
    const rows = await job.getQueryResults();

    job.getMetadata().then(response => {
      const [metadata] = response;
      this.logger.info('Executed BigQuery job', {
        url: this.contextService.url,
        jobId: metadata.jobReference.jobId,
        executionDurationMs: metadata.statistics.finalExecutionDurationMs,
        bytesBilled: metadata.statistics.query.totalBytesBilled,
        dataset: this.contextService.datasetId,
        tables: this.contextService.tableNames,
        // costInDollars: (metadata.statistics.query.totalBytesBilled / 1024 ** 4) * 6.25, // current cost is $6.25/TiB
        // query: metadata.configuration.query.query,  // do we want to log the query?
      });
    });

    return rows;
  }

  public getType(): string {
    return DatabaseTypes.BigQuery;
  }
}
