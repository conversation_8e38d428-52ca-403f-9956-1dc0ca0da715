meta {
  name: UpdateInventoryProcessFlowArea
  type: http
  seq: 25
}

put {
  url: {{api_host}}/inventory/process-flow/areas/:areaId?view=multishuttle
  body: json
  auth: inherit
}

params:query {
  view: multishuttle
}

params:path {
  areaId: test
}

body:json {
  {
    "id": "4:705d58a9-14be-4518-a9b4-30f5e4c3280a:0",
    "position": {
      "x": 500.31633288076546,
      "y": 500.8113593957016
    }
  }
}
