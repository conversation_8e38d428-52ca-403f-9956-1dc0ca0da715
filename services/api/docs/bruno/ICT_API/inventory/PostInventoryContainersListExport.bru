meta {
  name: PostInventoryContainersListExport
  type: http
  seq: 40
}

post {
  url: {{api_host}}/inventory/containers/list/export
  body: json
  auth: inherit
}

body:json {
  {
    "limit": 50,
    "page": 1,
    "sortFields": [
      {
        "columnName": "sku",
        "isDescending": false
      }
    ],
    "filters": {
      "type": "single",
      "name": "sku",
      "comparison": "Like",
      "value": "%TB%"
    },
    "byPassConfigSetting": false,
    "columns": {
      "container_id": true,
      "location_id": true,
      "zone": true,
      "sku": true,
      "quantity": true,
      "last_activity_date": true,
      "last_cycle_count": true,
      "data_updated": true,
      "free_cycle_count": true
    }
  }
}
