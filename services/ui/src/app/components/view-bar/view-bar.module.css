.viewBar {
  width: 100%;
  background-color: var(--cds-layer);
  height: 48px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--cds-border-subtle);

  &.extended {
    height: auto;
    flex-wrap: wrap;
    border-width: 3px;

    .viewBarInner {
      flex-direction: column;
      align-items: flex-start;
    }

    .titleGroup {
      min-height: 48px;
    }
  }
}

.viewBarInner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 1rem;
}

.titleGroup {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.actionsGroup {
  display: flex;
  align-items: center;
  gap: 12px;
}

.dateRangeSelect {
  width: 12em;
}

.selectedMenuItem {
  background-color: var(--cds-background-selected);
}
