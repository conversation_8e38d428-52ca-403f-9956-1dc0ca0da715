import { RecentlyViewed, Save, SettingsAdjust } from "@carbon/icons-react";
import { <PERSON><PERSON>, Layer, Select, SelectItem } from "@carbon/react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useMenu } from "../../config/menu/use-menu";
import { useSafeLocation } from "../../hooks/use-safe-location";
import { findMenuItem } from "../../router/router.util";
import { DatePeriod, type DatePeriodRange } from "../../types";
import { FavoriteButton } from "../favorite-button";
import SaveViewModal from "../save-view-modal/save-view-modal";
import classes from "./view-bar.module.css";

export type ViewBarProps = {
  title: string | React.ReactNode;
  showDatePeriodRange?: boolean;
  onDatePeriodRangeChange?: (range: DatePeriodRange) => void;
  selectedDatePeriodRange?: DatePeriodRange;
  availableDateRanges?: DatePeriod[];
  children?: React.ReactNode;
  showSettings?: boolean;
  onSettingsClick?: () => void;
  hasConfiguratorAccess?: boolean;
  showSave?: boolean;
  onSaveClick?: () => void;
  saveEnabled?: boolean;
  showViewHistory?: boolean;
  onViewHistoryClick?: () => void;
  menuItemId?: string;
  isExtended?: boolean;
};

export function ViewBar({
  title,
  showDatePeriodRange: showDateRange = false,
  onDatePeriodRangeChange: onDateRangeChange,
  selectedDatePeriodRange: selectedRange,
  availableDateRanges,
  showSettings = false,
  onSettingsClick,
  showSave = false,
  onSaveClick,
  saveEnabled = false,
  hasConfiguratorAccess = false,
  showViewHistory = false,
  onViewHistoryClick,
  children,
  menuItemId,
  isExtended = false,
}: ViewBarProps) {
  const [isSaveModalOpen, setIsSaveModalOpen] = useState(false);
  const { t } = useTranslation();
  const location = useSafeLocation();
  const { menuItems } = useMenu(t);

  // Find the menu item ID
  const currentPath = location.pathname;
  const currentMenuItem = findMenuItem(menuItems, currentPath);
  const effectiveMenuItemId = menuItemId || currentMenuItem?.id;

  const allDateRangeOptions = [
    { value: DatePeriod.today, label: "Today" },
    { value: DatePeriod.yesterday, label: "Yesterday" },
    { value: DatePeriod.last7days, label: "Last 7 Days" },
    { value: DatePeriod.last14days, label: "Last 14 Days" },
    { value: DatePeriod.last30days, label: "Last 30 Days" },
    { value: DatePeriod.thisWeek, label: "This Week" },
    { value: DatePeriod.lastWeek, label: "Last Week" },
    { value: DatePeriod.thisMonth, label: "This Month" },
    { value: DatePeriod.lastMonth, label: "Last Month" },
  ];

  const dateRangeOptions = availableDateRanges
    ? allDateRangeOptions.filter((option) =>
        availableDateRanges.includes(option.value),
      )
    : allDateRangeOptions;

  const handleSaveButtonClick = () => {
    setIsSaveModalOpen(true);
  };

  const handleSaveModalClose = () => {
    setIsSaveModalOpen(false);
  };

  const handleSaveConfirm = () => {
    setIsSaveModalOpen(false);
    onSaveClick?.();
  };

  return (
    <div
      data-testid="ict-view-bar-header"
      className={`${classes.viewBar} ${isExtended ? classes.extended : ""}`}
    >
      <div className={classes.viewBarInner}>
        <div className={classes.titleGroup}>
          {typeof title === "string" ? (
            <h2 data-testid="view-bar-title" className={classes.title}>
              {t(title)}
            </h2>
          ) : (
            title
          )}
          {currentMenuItem && (
            <FavoriteButton menuItemId={effectiveMenuItemId} />
          )}
        </div>
        <div className={classes.actionsGroup}>
          <Layer>
            {showDateRange && dateRangeOptions.length > 0 && (
              <div className={classes.dateRangeSelect}>
                <Select
                  id="date-range-select"
                  labelText=""
                  hideLabel
                  value={selectedRange ? String(selectedRange) : ""}
                  onChange={(e) =>
                    onDateRangeChange?.(e.target.value as DatePeriod)
                  }
                  size="sm"
                >
                  <SelectItem
                    disabled
                    hidden
                    value=""
                    text="Select date range"
                  />
                  {dateRangeOptions.map((option) => (
                    <SelectItem
                      key={option.value}
                      value={String(option.value)}
                      text={t(option.label)}
                    />
                  ))}
                </Select>
              </div>
            )}
          </Layer>
          {children}
          {showViewHistory && hasConfiguratorAccess && (
            <Button
              kind="ghost"
              size="sm"
              hasIconOnly
              renderIcon={RecentlyViewed}
              iconDescription="View History"
              onClick={onViewHistoryClick}
              tooltipPosition="bottom"
            />
          )}
          {showSave && saveEnabled && (
            <Button
              kind="primary"
              size="sm"
              hasIconOnly
              renderIcon={Save}
              iconDescription="Save"
              onClick={handleSaveButtonClick}
              tooltipPosition="bottom"
            />
          )}
          {showSettings && hasConfiguratorAccess && (
            <Button
              kind="ghost"
              size="sm"
              hasIconOnly
              renderIcon={SettingsAdjust}
              iconDescription="Settings"
              onClick={onSettingsClick}
              tooltipPosition="bottom"
            />
          )}
        </div>
      </div>

      <SaveViewModal
        open={isSaveModalOpen}
        onClose={handleSaveModalClose}
        onSave={handleSaveConfirm}
      />
    </div>
  );
}
