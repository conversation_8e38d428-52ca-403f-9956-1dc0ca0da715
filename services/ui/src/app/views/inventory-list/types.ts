import { components } from "../../../../../../libs/shared/ict-sdk/generated/openapi-react-query";

export type FacilitySkuItem = components["schemas"]["FacilityInventorySku"];
export type WmsSkuItem = components["schemas"]["WmsInventorySku"];
// Union type for both data sources
export type InventoryItem = FacilitySkuItem | WmsSkuItem;

export type InventoryListDataSource = "facility" | "wms";

export const wmsColumns = [
  { id: "sku", header: "Item" },
  {
    id: "quantityAvailable",
    header: "Quantity Available",
  },
  {
    id: "quantityAllocated",
    header: "Quantity Allocated",
  },
  {
    id: "maxContainers",
    header: "Max Containers",
  },
  { id: "skuPositions", header: "SKU Positions" },
  {
    id: "contOverage",
    header: "Overage",
  },
  {
    id: "daysOnHand",
    header: "Days on Hand (DOH)",
  },
  {
    id: "averageDailyQuantity",
    header: "Avg. Daily Qty.",
  },
  {
    id: "averageDailyOrders",
    header: "Avg. Daily Orders",
  },
  {
    id: "latestActivityDateTimestamp",
    header: "Last Activity Date",
  },
  {
    id: "latestCycleCountTimestamp",
    header: "Cycle Count Date",
  },
  { id: "description", header: "Description" },
  {
    id: "targetMultiplicity",
    header: "Target Multiplicity",
  },
  {
    id: "velocityClassification",
    header: "Velocity Classification",
  },
];

export const facilityColumns = [
  { id: "sku", header: "Item" },
  {
    id: "quantityAvailable",
    header: "Quantity Available",
  },
  {
    id: "quantityAllocated",
    header: "Quantity Allocated",
  },
  { id: "skuPositions", header: "SKU Positions" },
  {
    id: "daysOnHand",
    header: "Days on Hand (DOH)",
  },
  {
    id: "averageDailyQuantity",
    header: "Avg. Daily Qty.",
  },
  {
    id: "averageDailyOrders",
    header: "Avg. Daily Orders",
  },
  {
    id: "latestActivityDateTimestamp",
    header: "Last Activity Date",
  },
  { id: "description", header: "Description" },
];
