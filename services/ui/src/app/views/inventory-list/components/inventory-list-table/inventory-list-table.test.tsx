import type {
  ColumnFiltersState,
  PaginationState,
  SortingState,
} from "@tanstack/react-table";
import { vi } from "vitest";
import { fireEvent, render, screen } from "../../../../../test-utils";
import { InventoryListTable } from "./inventory-list-table";
import { InventoryListDataSource } from "../../types";

// Mock react-router
vi.mock("react-router", () => ({
  useNavigate: () => mockNavigate,
}));

// Mock Carbon React
vi.mock("@carbon/react", async () => {
  const actual = await vi.importActual("@carbon/react");
  return {
    ...actual,
    GlobalTheme: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock the Datagrid component
vi.mock("../../../../components/datagrid", () => ({
  Datagrid: ({
    columns,
    data,
    onPageChange,
    onSort,
    onFilter,
    onExport,
    onRefreshClick,
  }: {
    columns: any;
    data: any;
    onPageChange: any;
    onSort: any;
    onFilter: any;
    onExport: any;
    onRefreshClick: any;
  }) => (
    <div data-testid="datagrid">
      <div data-testid="columns">
        {JSON.stringify(columns.map((c: any) => c.header))}
      </div>
      <div data-testid="data">{JSON.stringify(data)}</div>
      <div data-testid="sku-cell">
        {columns
          .find((c: any) => c.id === "sku")
          ?.cell?.({
            getValue: () => data[0]?.sku,
          })}
      </div>
      <button
        type="button"
        data-testid="page-change"
        onClick={() => onPageChange({ pageIndex: 1, pageSize: 10 })}
      >
        Change Page
      </button>
      <button
        type="button"
        data-testid="sort"
        onClick={() => onSort([{ id: "sku", desc: true }])}
      >
        Sort
      </button>
      <button
        type="button"
        data-testid="filter"
        onClick={() => onFilter({ filters: { sku: "ABC123" } })}
      >
        Filter
      </button>
      <button type="button" data-testid="export" onClick={onExport}>
        Export
      </button>
      {onRefreshClick && (
        <button type="button" data-testid="refresh" onClick={onRefreshClick}>
          Refresh
        </button>
      )}
    </div>
  ),
}));

const mockNavigate = vi.fn();
const mockSetPagination = vi.fn();
const mockSetSorting = vi.fn();
const mockSetColumnFilters = vi.fn();
const mockOnExport = vi.fn();
const mockOnRefresh = vi.fn();

describe("InventoryListTable", () => {
  const mockData: any[] = [
    {
      sku: "ABC123",
      description: "Test Item 1",
      daysOnHand: "10",
      averageDailyQuantity: "5",
      averageDailyOrders: 2,
      totalQuantity: "100",
      quantityAvailable: "80",
      locations: 3,
      status: "Active",
      latestInventorySnapshotTimestamp: "2023-05-01T12:00:00Z",
      latestActivityDateTimestamp: "2023-05-02T14:30:00Z",
    },
    {
      sku: "XYZ789",
      description: "Test Item 2",
      daysOnHand: 5,
      averageDailyQuantity: 10,
      averageDailyOrders: 4,
      totalQuantity: 200,
      quantityAvailable: 150,
      locations: 2,
      status: "Active",
      latestInventorySnapshotTimestamp: "2023-05-01T10:00:00Z",
      latestActivityDateTimestamp: "2023-05-02T11:30:00Z",
    },
  ];

  const defaultProps = {
    data: mockData,
    pagination: { pageIndex: 0, pageSize: 10 } as PaginationState,
    setPagination: mockSetPagination,
    sorting: [] as SortingState,
    setSorting: mockSetSorting,
    columnFilters: [] as ColumnFiltersState,
    setColumnFilters: mockSetColumnFilters,
    isLoading: false,
    isFetching: false,
    error: null,
    rowCount: 2,
    dataSource: "facility" as InventoryListDataSource,
    onExport: mockOnExport,
    setGlobalFilter: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render the datagrid with correct data", () => {
    render(<InventoryListTable {...defaultProps} />);

    expect(screen.getByTestId("datagrid")).toBeInTheDocument();

    const dataElement = screen.getByTestId("data");
    const parsedData = JSON.parse(dataElement.textContent || "[]");

    expect(parsedData).toHaveLength(2);
    expect(parsedData[0].sku).toBe("ABC123");
    expect(parsedData[1].sku).toBe("XYZ789");
  });

  it("should render all expected columns", () => {
    render(<InventoryListTable {...defaultProps} />);

    const columnsElement = screen.getByTestId("columns");
    const columnHeaders = JSON.parse(columnsElement.textContent || "[]");

    const expectedHeaders = [
      "SKU",
      "Qty Available",
      "Qty Allocated",
      "SKU Positions",
      "Days On Hand",
      "Avg Daily Qty",
      "Avg Daily Orders",
      "Latest Activity",
      "Description",
    ];

    expect(columnHeaders).toEqual(expectedHeaders);
  });

  it("should handle pagination changes", () => {
    render(<InventoryListTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("page-change"));

    expect(mockSetPagination).toHaveBeenCalledWith({
      pageIndex: 1,
      pageSize: 10,
    });
  });

  it("should handle sorting changes", () => {
    render(<InventoryListTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("sort"));

    expect(mockSetSorting).toHaveBeenCalledWith([{ id: "sku", desc: true }]);
  });

  it("should handle filter changes", () => {
    render(<InventoryListTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("filter"));

    expect(mockSetColumnFilters).toHaveBeenCalledWith([
      { id: "sku", value: "ABC123" },
    ]);
  });

  it("should handle export button click", () => {
    render(<InventoryListTable {...defaultProps} />);

    fireEvent.click(screen.getByTestId("export"));

    expect(mockOnExport).toHaveBeenCalled();
  });

  it("should show loading state", () => {
    render(<InventoryListTable {...defaultProps} isLoading={true} />);

    const datagrid = screen.getByTestId("datagrid");
    expect(datagrid).toBeInTheDocument();
  });

  it("should show error state", () => {
    render(<InventoryListTable {...defaultProps} error="Test error" />);

    const datagrid = screen.getByTestId("datagrid");
    expect(datagrid).toBeInTheDocument();
  });

  it("should handle refresh button click when onRefresh is provided", () => {
    render(<InventoryListTable {...defaultProps} onRefresh={mockOnRefresh} />);

    fireEvent.click(screen.getByTestId("refresh"));

    expect(mockOnRefresh).toHaveBeenCalled();
  });

  it("should not show refresh button when onRefresh is not provided", () => {
    render(<InventoryListTable {...defaultProps} />);

    expect(screen.queryByTestId("refresh")).not.toBeInTheDocument();
  });
});
