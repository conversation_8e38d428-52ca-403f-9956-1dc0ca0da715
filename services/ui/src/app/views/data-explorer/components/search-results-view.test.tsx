import { components } from "@ict/sdk/openapi-react-query";
import { render, screen, userEvent } from "../../../../test-utils";
import SearchResultsView from "./search-results-view";
import { useConfigSetting } from "../../../config/hooks/use-config";
import { useRoles } from "../../../auth/hooks/use-roles";
import { SecurityRoles } from "@ict/sdk/types";
import { ictApi } from "../../../api/ict-api";
import { fail } from "assert";

// mock the api client
vi.mock("../../../api/ict-api", () => ({
  ictApi: {
    client: {
      useQuery: vi.fn(),
      useMutation: vi.fn(),
    },
  },
}));

// mock the use-config hook
vi.mock("../../../config/hooks/use-config", () => ({
  useConfigSetting: vi.fn().mockReturnValue({
    setting: {
      value: true,
    },
  }),
}));

// mock the use-roles hook
vi.mock("../../../auth/hooks/use-roles", () => ({
  useRoles: vi.fn().mockReturnValue({
    roles: ["ct_engineers"],
  }),
}));

describe(SearchResultsView.name, () => {
  const mockUseConfigSetting = vi.mocked(useConfigSetting);
  const mockUseRoles = vi.mocked(useRoles);
  const useQueryMock = vi.mocked(ictApi.client.useQuery);
  const useMutationMock = vi.mocked(ictApi.client.useMutation);

  const mockResult: components["schemas"]["DataExplorerResult"] = {
    id: "1",
    prompt: "test question",
    answer: "test answer",
    queryResults: [{}],
    isRecommended: "not_provided",
    timestamp: "2024-01-01T00:00:00.000Z",
    debugData: {
      ResponseCode: 200,
      question: "debug test question",
      summary_response: "debug test anwser",
    },
  };

  beforeEach(() => {
    mockUseConfigSetting.mockReturnValue({
      setting: {
        value: true,
        id: "",
        name: "facility-maps",
        group: null,
        dataType: "string",
      },
      isLoading: false,
      error: null,
    });
    mockUseRoles.mockReturnValue({
      roles: [SecurityRoles.CT_ENGINEERS],
      actualRoles: [SecurityRoles.CT_ENGINEERS],
      isInternalUser: true,
      hasTableauAccess: false,
      isLoading: false,
      hasConfiguratorAccess: true,
      isActualInternalUser: true,
      isFacilityAdmin: false,
      isInternalEmployee: false,
    });
    // @ts-ignore
    useQueryMock.mockReturnValue({
      data: mockResult,
    });
    useMutationMock.mockReturnValue({
      mutate: vi.fn(),
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("handles no search result", () => {
    useQueryMock.mockReturnValue({
      data: undefined,
    });
    render(<SearchResultsView resultId={"1"} />);
    expect(screen.getByText("No results")).toBeInTheDocument();
  });

  it("renders the search result", () => {
    render(<SearchResultsView resultId={"1"} />);
    expect(screen.getByText("test question")).toBeInTheDocument();
    expect(
      screen.getByText(
        `Answer returned ${new Date(mockResult.timestamp).toLocaleString()}`,
      ),
    ).toBeInTheDocument();
  });

  it("renders debug data when user has the 'ct_engineers' role, the 'data-explorer-debug-data' flag is true", () => {
    render(<SearchResultsView resultId={"1"} />);
    expect(
      screen.getByRole("button", { name: "Debug Data" }),
    ).toBeInTheDocument();
    expect(
      screen.getByRole("textbox", { name: "Debug Data" }).innerHTML,
    ).toContain("debug test question");
  });

  it("should not render debug data when user doesn't have the 'ct_engineers' role", () => {
    mockUseRoles.mockReturnValue({
      roles: [],
      actualRoles: [],
      isInternalUser: false,
      hasTableauAccess: false,
      isLoading: false,
      hasConfiguratorAccess: false,
      isActualInternalUser: false,
      isFacilityAdmin: false,
      isInternalEmployee: false,
    });
    render(<SearchResultsView resultId={"1"} />);
    expect(screen.queryByRole("button", { name: "Debug Data" })).toBeNull();
  });

  it("should not render debug data when the 'data-explorer-debug-data' flag is false", () => {
    mockUseConfigSetting.mockReturnValue({
      setting: {
        id: "id",
        name: "data-explorer-debug-data",
        group: null,
        dataType: "boolean",
        value: false,
      },
      isLoading: false,
      error: null,
    });
    render(<SearchResultsView resultId={"1"} />);
    expect(screen.queryByRole("button", { name: "Debug Data" })).toBeNull();
  });

  it.each(["Thumbs up", "Thumbs down", "Bookmark"])(
    "makes a mutate call when the %s button is clicked",
    async (buttonName) => {
      const mutateMock = vi.fn();
      useMutationMock.mockReturnValue({
        mutate: mutateMock,
      });
      const component = render(<SearchResultsView resultId={"1"} />);
      const button = component.container.querySelector(
        `button > svg[aria-label="${buttonName}"]`,
      );
      if (!button) fail(`${buttonName} button not found`);
      await userEvent.click(button);
      expect(mutateMock).toHaveBeenCalled();
    },
  );
});
