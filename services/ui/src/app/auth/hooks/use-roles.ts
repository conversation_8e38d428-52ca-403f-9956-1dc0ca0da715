import { SecurityRoles } from "@ict/sdk/types";
import { useEffect, useState } from "react";
import { Logger } from "../../utils/logger";
import { useAuth } from "./use-auth";
import {
  ROLE_PREVIEW_EVENT,
  type RolePreviewChangeEvent,
} from "./use-role-preview";

const ROLE_OVERRIDES_KEY = "role-preview-disabled-roles";
const logger = new Logger("use-roles");

/**
 * Hook to manage user roles and admin status.
 * This hook will fetch and cache the user's roles from their token claims,
 * and apply any role overrides from the role preview feature.
 */
export const useRoles = () => {
  const { getIdTokenClaims, user } = useAuth();
  const [actualRoles, setActualRoles] = useState<string[]>([]);
  const [effectiveRoles, setEffectiveRoles] = useState<string[]>([]);
  const [disabledRoles, setDisabledRoles] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Get the actual roles from the token claims
  useEffect(() => {
    const checkRoles = async () => {
      const claims = await getIdTokenClaims();
      const userRoles = claims?.["https://ict.dematic.cloud/roles"] || [];
      setActualRoles(userRoles);
      setIsLoading(false);
    };

    checkRoles();
  }, [getIdTokenClaims]);

  // Listen for role preview change events
  useEffect(() => {
    const handleRolePreviewChange = (event: Event) => {
      const { disabledRoles } = (event as RolePreviewChangeEvent).detail;
      setDisabledRoles(disabledRoles);
    };

    window.addEventListener(ROLE_PREVIEW_EVENT, handleRolePreviewChange);

    return () => {
      window.removeEventListener(ROLE_PREVIEW_EVENT, handleRolePreviewChange);
    };
  }, []);

  // Load initial disabled roles from localStorage
  useEffect(() => {
    const storedDisabledRoles = localStorage.getItem(ROLE_OVERRIDES_KEY);

    if (storedDisabledRoles) {
      try {
        const parsedRoles = JSON.parse(storedDisabledRoles);
        setDisabledRoles(parsedRoles);
      } catch (error) {
        logger.error("Error parsing disabled roles:", error);
      }
    }
  }, []);

  // Apply role overrides whenever actualRoles or disabledRoles change
  useEffect(() => {
    if (actualRoles.length > 0) {
      // Filter out disabled roles
      const roles = actualRoles.filter((role) => !disabledRoles.includes(role));
      setEffectiveRoles(roles);
    }
  }, [actualRoles, disabledRoles]);

  // Compute role-based permissions using effective roles
  const isInternalUser =
    effectiveRoles.includes(SecurityRoles.CT_ENGINEERS) ||
    effectiveRoles.includes(SecurityRoles.CT_CONFIGURATORS);

  const internalEmails = ["@dematic.com", "@kiongroup.com"];
  const isInternalEmployee = internalEmails.some((email) =>
    (user?.email ?? "").endsWith(email),
  );

  const hasTableauAccess =
    effectiveRoles.includes(SecurityRoles.CT_TABLEAU_VIEWER) ||
    effectiveRoles.includes(SecurityRoles.CT_TABLEAU_EXPLORER) ||
    effectiveRoles.includes(SecurityRoles.CT_TABLEAU_ADMIN);

  const hasConfiguratorAccess = effectiveRoles.includes(
    SecurityRoles.CT_CONFIGURATORS,
  );

  // Compute actual role-based permissions (ignoring overrides)
  const isActualInternalUser =
    actualRoles.includes(SecurityRoles.CT_ENGINEERS) ||
    actualRoles.includes(SecurityRoles.CT_CONFIGURATORS);

  const isFacilityAdmin = effectiveRoles.some((role) =>
    role.includes("_facility_admin"),
  );

  return {
    /**
     * The auth0 roles that the currently logged in user has,
     * after applying any role preview overrides
     */
    roles: effectiveRoles,

    /**
     * The actual auth0 roles that the user has,
     * ignoring any role preview overrides
     */
    actualRoles,

    /**
     * Returns true if the user has the ct_engineers or ct_configurator role
     * after applying any role preview overrides
     */
    isInternalUser,

    /**
     * Returns true if the user has the ct_tableau_viewer, ct_tableau_explorer, or ct_tableau_admin role
     * after applying any role preview overrides
     */
    hasTableauAccess,

    /**
     * Returns true if the user has the ct_configurator role
     * after applying any role preview overrides
     */
    hasConfiguratorAccess,

    /**
     * Returns true if the user actually has the ct_engineers or ct_configurator role,
     * ignoring any role preview overrides
     */
    isActualInternalUser,

    /**
     * Returns true if the user has a facility admin role
     */
    isFacilityAdmin,

    /**
     * Returns true if the user's roles are still loading
     */
    isLoading,

    /**
     * Returns true if the user's email domain is one of Dematic.com or Kiongroup.com
     */
    isInternalEmployee,
  };
};
