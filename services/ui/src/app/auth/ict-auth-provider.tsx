import { Auth0Provider } from "@auth0/auth0-react";
import { ReactNode, useEffect } from "react";
import { authService } from "./auth-service";
import { IctAuthContext } from "./ict-auth-context";
import { useAuth } from "./hooks/use-auth";

interface IctAuthProviderProps {
  children: ReactNode;
}

/**
 * Internal component that handles auth initialization after Auth0 is set up
 */
function AuthInitializer({ children }: { children: ReactNode }) {
  const auth0Client = useAuth();

  useEffect(() => {
    authService.setAuth0Client(auth0Client);
  }, [auth0Client]);

  return children;
}

/**
 * Main auth provider component that sets up Auth0 and initializes our auth service.
 * This replaces the need for separate Auth0Provider setup and useInitializeAuth calls.
 */
export function IctAuthProvider({ children }: IctAuthProviderProps) {
  return (
    <Auth0Provider
      context={IctAuthContext}
      domain={import.meta.env.VITE_AUTH0_DOMAIN}
      clientId={import.meta.env.VITE_AUTH0_CLIENT_ID}
      authorizationParams={{
        redirect_uri: window.location.origin,
        audience: import.meta.env.VITE_AUTH0_AUDIENCE,
        scope: "openid profile email",
      }}
      useRefreshTokensFallback={true}
      cacheLocation="localstorage"
      useRefreshTokens={true}
    >
      <AuthInitializer>{children}</AuthInitializer>
    </Auth0Provider>
  );
}
