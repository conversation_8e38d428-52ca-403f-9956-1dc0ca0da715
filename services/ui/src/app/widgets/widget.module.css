.widgetWrapper {
  height: 100%;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.widgetHeader {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
}

.widgetContent {
  flex: 1;
  min-height: 0;
  /* Important for Firefox */
}

.optionsPanel {
  position: absolute;
  top: 100%;
  right: 0;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 100;
  min-width: 200px;
}

.optionsFormContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden;
  padding-bottom: 50px;
}

.optionsFormContent {
  flex: 1;
  overflow-y: auto;
}

.optionsFormActions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  background-color: var(--cds-layer);
  border-top: 1px solid var(--cds-border-subtle);
  width: 100%;
}

.actionButton {
  flex: 1;
  max-width: calc(50% - 0.5rem);
}
