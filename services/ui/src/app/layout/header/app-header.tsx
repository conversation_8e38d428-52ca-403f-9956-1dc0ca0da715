import { useState } from "react";
import {
  Header as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>erG<PERSON>bal<PERSON>ar,
  HeaderMenu<PERSON>utt<PERSON>,
  <PERSON>er<PERSON><PERSON>,
  <PERSON>ack,
  Toggle,
  <PERSON><PERSON><PERSON>,
  Pop<PERSON>,
  PopoverContent,
} from "@carbon/react";
import {
  User,
  Help,
  Moon,
  Enterprise,
  Logout,
  Checkmark,
  Information,
} from "@carbon/icons-react";
import { useAuth } from "../../auth/hooks/use-auth";
import { useRoles } from "../../auth/hooks/use-roles";
import { SiteTimeText } from "../../components/site-time-text/site-time-text";
import {
  useConfigSetting,
  useFeatureFlag,
} from "../../config/hooks/use-config";
import logoDark from "../../../assets/brand/logo-dark.png";
import logoLight from "../../../assets/brand/logo-light.png";
import { ThemeMode, useTheme } from "../theme";
import styles from "./app-header.module.css";
import { SecurityRoles } from "@ict/sdk/types";
import { useRolePreview } from "../../auth/hooks/use-role-preview";
import { FacilitySwitcher } from "./components/facility-switcher";
import { useSafeLocation } from "../../hooks/use-safe-location";
import { useMenu } from "../../config/menu/use-menu";
import { findMenuItem, findMenuItemById } from "../../router/router.util";
import { useTranslation } from "react-i18next";
import { logger } from "../../utils/logger";
import { TABLEAU_MENU_MAIN_PATH_SEGMENT } from "../../config/menu/transformers/tableau-transformer";

interface HeaderProps {
  isMenuOpen: boolean;
  onMenuToggle: () => void;
}

export function AppHeader({ isMenuOpen, onMenuToggle }: HeaderProps) {
  const [isUserPanelExpanded, setIsUserPanelExpanded] = useState(false);
  const { theme, toggleTheme } = useTheme();
  const isDarkTheme = theme === ThemeMode.DARK;
  const logoSrc = isDarkTheme ? logoDark : logoLight;
  const { user, logout, loginWithRedirect } = useAuth();
  const { actualRoles, isActualInternalUser, isInternalEmployee } = useRoles();
  const { setting: timezoneConfig } = useConfigSetting("site-time-zone");
  const organization = user?.organization?.display_name;
  const { disabledRoles, toggleRole, resetRoles } = useRolePreview(actualRoles);
  const { t } = useTranslation();
  const location = useSafeLocation();
  const { menuItems } = useMenu(t);
  const { enabled: helpFeatureEnabled } = useFeatureFlag(
    "ict-nav-help-content",
  );

  // Function to get the help link for the current page
  const getCurrentPageHelpLink = () => {
    const segments = location.pathname.split("/");
    const rootPath =
      segments.length > 1 ? `/${segments[1]}` : location.pathname;
    const currentMenuItem = findMenuItem(menuItems, rootPath);
    logger.debug(
      `Menu item for ${rootPath}: ${JSON.stringify(currentMenuItem)}`,
    );
    if (currentMenuItem?.helpLink) {
      return currentMenuItem.helpLink;
    } else if (
      rootPath === TABLEAU_MENU_MAIN_PATH_SEGMENT &&
      !currentMenuItem?.parentId
    ) {
      // edge case to handle the weirdness of tableau menu items
      logger.debug(
        "User is viewing a Tableau workbook. Finding the performance analysis menu item to determine the help link.",
      );
      const performanceAnalysisItem = findMenuItemById(
        menuItems,
        "performance-analysis",
      );
      if (performanceAnalysisItem?.helpLink) {
        return performanceAnalysisItem.helpLink;
      }
    } else {
      // If the current menu item does not have a help link, we need to find the parent menu item that has a help link
      // recursively move up the menu tree until we find a menu item that has a help link
      let parentId = currentMenuItem?.parentId;
      while (parentId && parentId.length > 0) {
        const parentMenuItem = findMenuItemById(menuItems, parentId);
        if (parentMenuItem?.helpLink) {
          return parentMenuItem.helpLink;
        } else if (!parentMenuItem) {
          // if there is no parent menu item, we're at the top level with no help link
          break;
        }
        // this menu item doesn't have a help link but it has a parent, so we need to move up the menu tree
        parentId = parentMenuItem?.parentId;
      }
    }

    return "r/Dematic-Control-Tower/User-Guide/en-US/About-Dematic-Control-Tower";
  };

  const handleLogout = () => {
    logout({
      logoutParams: {
        returnTo: window.location.origin,
      },
    });
  };

  const switchOrg = () => {
    loginWithRedirect();
  };

  const handleHelpClick = () => {
    const currentHelpLink = getCurrentPageHelpLink();
    if (currentHelpLink && typeof currentHelpLink === "string") {
      // Navigate to the specific help page. Handle double forward slashes appropriately
      const baseUrl = import.meta.env.VITE_DOCUMENTATION_URL.replace(/\/$/, "");
      const helpPath = currentHelpLink.replace(/^\//, "");
      window.open(`${baseUrl}/${helpPath}?control_tower=true`, "_blank");
    } else {
      // Fallback to generic documentation
      window.open(
        import.meta.env.VITE_DOCUMENTATION_URL + "?control_tower=true",
        "_blank",
      );
    }
  };

  return (
    <CarbonHeader>
      {/* Hamburger Menu */}
      <HeaderMenuButton
        aria-label={isMenuOpen ? "Close menu" : "Open menu"}
        onClick={onMenuToggle}
        isActive={isMenuOpen}
        isCollapsible={true}
        className={styles.hamburgerMenu}
      />

      {/* Dematic Logo */}
      <HeaderName prefix="" href="/">
        <img src={logoSrc} alt="Dematic Logo" className={styles.logo} />
      </HeaderName>

      {/* Right Side Actions */}
      <HeaderGlobalBar className={styles.rightSideContent}>
        <Stack
          orientation="horizontal"
          style={{ cursor: "default" }}
          className={styles.locationTimeContainer}
          gap="1.5rem"
        >
          {/* Organization */}
          <Stack
            orientation="horizontal"
            style={{ cursor: "default" }}
            gap="1rem"
          >
            <Enterprise size={14} className={styles.locationIcon} />
            <span data-testid="ict-organization" className={styles.tenantName}>
              {organization}
            </span>
          </Stack>

          {/* Facility Selector */}
          <FacilitySwitcher />

          {/* Time */}
          <SiteTimeText
            showIcon={false}
            siteTimezone={timezoneConfig?.value as string}
          />
        </Stack>

        {/* Help (link to fluidtopics) */}
        {helpFeatureEnabled && (
          <button
            title="Documentation"
            onClick={handleHelpClick}
            className={styles.headerButton}
          >
            <Help size={18} />
          </button>
        )}

        <Popover
          open={isUserPanelExpanded}
          align="bottom-right"
          autoAlign
          isTabTip
          aria-label="User Account Panel"
          onRequestClose={() => setIsUserPanelExpanded(false)}
        >
          <div>
            <button
              title={isUserPanelExpanded ? "Close" : "Account"}
              onClick={() => setIsUserPanelExpanded((prev) => !prev)}
              className={`${styles.headerButton} ${isUserPanelExpanded ? "cds--header__action--active" : ""}`}
              data-testid="user-profile-menu"
              aria-label="Account"
            >
              <User size={20} />
            </button>
          </div>
          <PopoverContent>
            <div className={styles.headerPanelContent}>
              {/* User Profile Section */}
              <div>
                <div className={styles.profileHeader}>
                  <span className={styles.profileSignedInAs}>Signed in as</span>
                  <span
                    data-testid="user-profile-email"
                    className={styles.profileEmail}
                  >
                    {user?.email}
                  </span>
                </div>
              </div>

              {/* Main Actions Section */}
              <div className={styles.actionsSection}>
                <div className={styles.actionItem} onClick={toggleTheme}>
                  <div className={styles.actionItemContent}>
                    <Moon size={16} />
                    <span>Dark Mode</span>
                  </div>
                  <Toggle
                    id="theme-toggle"
                    size="sm"
                    toggled={isDarkTheme}
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      toggleTheme();
                    }}
                    aria-label="Dark Mode"
                    hideLabel
                  />
                </div>
                {/* only show the switch org button if user's an internal employee or we're in a non-prod env */}
                {(isInternalEmployee ||
                  import.meta.env.VITE_ENVIRONMENT !== "prod") && (
                  <div
                    className={styles.actionItem}
                    onClick={switchOrg}
                    role="button"
                    tabIndex={0}
                    data-testid="switch-organization"
                  >
                    <div className={styles.actionItemContent}>
                      <Enterprise size={16} />
                      <span>Switch Organization</span>
                    </div>
                  </div>
                )}
                <div
                  className={`${styles.actionItem} ${styles.actionItemDanger}`}
                  onClick={handleLogout}
                  role="button"
                  tabIndex={0}
                  data-testid="log-out"
                >
                  <div className={styles.actionItemContent}>
                    <Logout size={16} />
                    <span>Log Out</span>
                  </div>
                </div>
              </div>

              {/* Role Preview Section - Only for internal users */}
              {isActualInternalUser && (
                <div className={styles.roleSection} data-testid="role-preview">
                  <div className={styles.roleHeader}>
                    <div className={styles.roleTitle}>
                      <div className={styles.roleTitleContent}>
                        <span>Role Preview</span>
                        <Tooltip
                          label="Temporarily disable roles to preview the application with limited permissions"
                          autoAlign
                        >
                          <button className={styles.tooltipTrigger}>
                            <Information size={16} />
                          </button>
                        </Tooltip>
                      </div>
                      {disabledRoles.length > 0 && (
                        <button
                          className={styles.resetLink}
                          onClick={resetRoles}
                        >
                          Reset
                        </button>
                      )}
                    </div>
                  </div>

                  <div className={styles.roleList}>
                    {Object.values(SecurityRoles).map((role) => {
                      const hasRole = actualRoles.includes(role);
                      const isRoleEnabled =
                        hasRole && !disabledRoles.includes(role);

                      return hasRole ? (
                        <div
                          key={role}
                          className={`${styles.roleItem} ${isRoleEnabled ? styles.roleItemChecked : ""}`}
                          onClick={() => toggleRole(role)}
                        >
                          <div className={styles.roleItemContent}>
                            <span>
                              {role.replace(/_/g, " ").replace(/ct/g, "CT")}
                            </span>
                          </div>
                          {isRoleEnabled && (
                            <Checkmark
                              size={16}
                              className={styles.roleItemIcon}
                            />
                          )}
                        </div>
                      ) : null;
                    })}
                  </div>
                </div>
              )}
            </div>
          </PopoverContent>
        </Popover>
      </HeaderGlobalBar>
    </CarbonHeader>
  );
}
