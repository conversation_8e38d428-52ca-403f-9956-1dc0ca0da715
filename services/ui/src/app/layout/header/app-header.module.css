/* Header Layout Components */
.hamburgerMenu {
  margin-right: 1rem;
}

.logo {
  height: 24px;
}

.rightSideContent {
  display: flex;
  align-items: center;
}

.headerButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  color: var(--cds-text-secondary);
}

.headerButton:hover {
  background-color: var(--cds-background-hover);
}

/* Organization/Location Section */
.locationTimeContainer {
  font-size: 0.875rem;
  align-items: center;
  padding-right: 1.5rem;
  margin-right: 0.75rem;
  color: var(--cds-text-secondary);
  border-right: 1px solid var(--cds-border-subtle-01);
}

.locationIcon {
  color: var(--cds-icon-secondary);
}

.tenantName {
  color: var(--cds-text-secondary);
}

/* Header Panel */
.headerPanelContent {
  background-color: var(--cds-layer);
  height: 100%;
  min-width: 300px;
}

:global(.cds--header-panel) {
  overflow: visible !important;
}

/* Profile Section */
.profileHeader {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
}

.profileSignedInAs {
  font-size: 0.75rem;
  color: var(--cds-text-secondary);
}

.profileEmail {
  font-size: 0.875rem;
  color: var(--cds-text-primary);
}

/* Actions Section */
.actionsSection {
  padding: 0;
}

.actionItem {
  height: 2.5rem;
  padding: 0 0.5rem 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--cds-text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
}

.actionItemContent {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.actionItemContent svg {
  color: var(--cds-icon-secondary);
}

.actionItem:hover {
  background-color: var(--cds-layer-hover);
  color: var(--cds-text-primary);
}

.actionItem:hover .actionItemContent svg {
  color: var(--cds-icon-primary);
}

.actionItemDanger {
  color: var(--cds-text-error);
}

.actionItemDanger .actionItemContent svg {
  color: var(--cds-icon-error);
}

.actionItemDanger:hover {
  background-color: var(--cds-layer-hover);
  color: var(--cds-text-error);
}

.actionItemDanger:hover .actionItemContent svg {
  color: var(--cds-icon-error);
}

/* Role Preview Section */
.roleSection {
  padding: 0;
}

.roleHeader {
  margin-top: 1rem;
  padding: 0 1rem;
}

.roleTitle {
  padding-top: 1.5rem;
  margin-bottom: 0.75rem;
  border-top: 1px solid var(--cds-border-subtle-01);
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--cds-text-primary);
  font-size: 0.875rem;
}

.roleTitleContent {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.tooltipTrigger {
  background: none;
  border: none;
  padding: 0;
  display: flex;
  align-items: center;
  color: var(--cds-icon-secondary);
}

.tooltipTrigger:hover {
  color: var(--cds-icon-primary);
}

.resetLink {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  color: var(--cds-link-primary);
  font-size: 0.875rem;
  cursor: pointer;
  text-decoration: none;
  font-weight: 400;
  line-height: 0rem;
}

.resetLink:hover {
  color: var(--cds-link-primary-hover);
  text-decoration: underline;
}

.roleList {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1.5rem;
}

.roleItem {
  height: 2.5rem;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--cds-text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 110ms;
}

.roleItem:hover:not(.roleItemDisabled) {
  background-color: var(--cds-layer-hover);
  color: var(--cds-text-primary);
}

.roleItemContent {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.roleItemDisabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.roleItemChecked {
  color: var(--cds-text-secondary);
}

.roleItemIcon {
  color: var(--cds-icon-primary);
}
