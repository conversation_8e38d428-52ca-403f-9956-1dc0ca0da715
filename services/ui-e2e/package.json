{"name": "@ict/ui-e2e", "description": "The ui-carbon-e2e is test automation framework based on Playwright.", "version": "1.0.0", "engines": {"node": ">=20.0.0"}, "scripts": {"setup": "yarn playwright install && brew install allure", "docs:build": "npx typedoc --tsconfig tsconfig.json --options typedoc.config.mjs --readme ./README.md", "docs": "yarn docs:build; open ./docs/index.html", "report:clean": "rimraf ./results/* ", "report:build": "allure generate ./results/allure/allure-results -o ./results/allure/allure-report --clean --name \"'ICT Carbon E2E Test Results'\" ", "report": "yarn report:build && allure open ./results/allure/allure-report", "test": "yarn report:clean && npx playwright test; yarn report", "test:debug": "npx playwright test tests/regression --headed", "test:smoke": "yarn report:clean && npx playwright test tests/smoke --grep @suite:smoke; yarn report", "test:smoke:ci": "npx playwright test tests/smoke --grep @suite:smoke", "test:login": "yarn report:clean && npx playwright test tests/regression/login --grep @suite:login; yarn report", "test:login:ci": "npx playwright test tests/regression/login --grep @suite:login", "test:regression": "yarn report:clean && npx playwright test tests/regression/* --grep @suite:regression; yarn report", "test:regression:ci": "npx playwright test tests/regression/* --grep @suite:regression", "test:regression:full": "yarn report:clean && npx playwright test --grep \"@suite:regression|@suite:charts|@suite:visual\"; yarn report", "test:regression:full:ci": "npx playwright test tests/regression/* --grep \"@suite:regression|@suite:charts|@suite:visual\"", "build": "tsc --incremental -p tsconfig.json", "lint": "eslint --fix"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@playwright/test": "^1.51.1", "@testomatio/reporter": "^1.6.16", "auth0": "^4.1.0", "axios": "^1.9.0", "luxon": "^3.4.4", "minimatch": "^10.0.1", "reflect-metadata": "^0.2.2", "ts-retry": "^4.2.5", "tslog": "^4.9.3", "typedi": "^0.10.0", "uuid": "^10.0.0", "xlsx": "0.18.5", "zod": "^3.23.8", "jwt-decode": "^4.0.0"}, "devDependencies": {"@eslint/js": "9.33.0", "@open-wc/eslint-config": "12.0.3", "@types/uuid": "9.0.8", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "allure-js-commons": "3.2.2", "allure-playwright": "3.2.2", "config": "4.0.0", "cross-env": "7.0.3", "dotenv": "16.5.0", "dpdm": "3.14.0", "eslint": "9.33.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-unicorn": "57.0.0", "open": "10.1.2", "prettier": "3.6.2", "rimraf": "6.0.1", "typedoc": "0.28.10", "typescript": "5.8.3", "typescript-eslint": "8.34.0"}}