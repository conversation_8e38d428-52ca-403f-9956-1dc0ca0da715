import path from 'path';
import { defineConfig, ReporterDescription } from '@playwright/test';
export const CHROMIUM_STORAGE_STATE = './chromium.cookies.json';
export const FIREFOX_STORAGE_STATE = './firefox.cookies.json';

export default defineConfig({
  testDir: './tests',
  // Each test is given 2 min (if a test needs more time this can set at the test or suite level)
  timeout: 60000 * 3,
  fullyParallel: true,
  workers: process.env.CI ? 8 : 8,
  reportSlowTests: { max: 60000 * 5, threshold: 60000 * 6 },
  retries: process.env.CI ? 3 : 0,
  expect: {
    timeout: 30000,
  },
  reporter: configureReporters(),
  globalSetup: './scripts/global-setup.ts',
  use: {
    actionTimeout: 30000,
    video: {
      mode: 'on-first-retry',
      size: { width: 640, height: 480 },
    },
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    viewport: { width: 1500, height: 740 },
    headless: true,
    navigationTimeout: 60000 * 3,
    launchOptions: {
      slowMo: 5,
    },
  },
  projects: [
    {
      name: 'setup',
      testMatch: 'setup.ts',
      testDir: './scripts',
    },
    {
      name: 'smoke',
      testDir: 'tests/smoke',
    },
    {
      name: 'login',
      testMatch: /login\.ts/,
      testDir: 'tests/regression/login',
    },
    {
      name: 'Regression - Chromium',
      testDir: 'tests/regression',
      dependencies: ['setup'],
      use: {
        storageState: CHROMIUM_STORAGE_STATE,
        browserName: 'chromium',
      },
    },
    {
      name: 'Regression - Firefox',
      testDir: 'tests/regression',
      dependencies: ['setup'],
      use: {
        storageState: FIREFOX_STORAGE_STATE,
        browserName: 'firefox',
      },
    },
  ],
});

function configureReporters(): ReporterDescription[] {
  const reporters: ReporterDescription[] = [];
  reporters.push([
    'allure-playwright',
    {
      resultsDir: path.join(process.cwd(), 'results', 'allure', 'allure-results'),
      detail: true,
      suiteTitle: true,
      links: {
        jira: {
          urlTemplate: (v) => `https://jira.dematic.net/browse/${v}`,
        },
      },
    },
  ]);
  reporters.push(['html', { open: 'never', outputFolder: './results/html' }]);
  reporters.push(['junit', { outputFile: './results/junit/ict-test-results.xml' }]);
  reporters.push(['list']);
  return reporters;
}
