import { Browser, chromium, Page, test as setup, firefox } from '@playwright/test';
import { AuthenticationService } from '@ui-adk/services/authentication';
import { ITestConfigurationService, TestConfigurationService } from '@ui-adk/services/configuration/test-configuration-service';
import Container from 'typedi';
import { Login } from '@ui-adk/views';
import { CHROMIUM_STORAGE_STATE, FIREFOX_STORAGE_STATE } from '../playwright.config';
import { AppConfigurationService } from '@ui-adk/services/configuration/app-configuration-service';

setup.describe.serial(() => {
  setup('Authenticate user', async () => {
    const config = Container.get(TestConfigurationService);
    await cacheStorageState_for_chromium(config);
    await cacheStorageState_for_firefox(config);
  });
});

async function cacheStorageState_for_chromium(config: ITestConfigurationService) {
  const browser: Browser = await chromium.launch();
  const context = await browser.newContext();

  const page: Page = await context.newPage();
  const app = new Login(page, config);

  await app.goTo();

  // Only intercept successful token and facility-config responses with content
  const tokenResponse = page.waitForResponse(
    (response) =>
      response.url().includes('/oauth/token') &&
      response.status() === 200 &&
      response.headers()['content-type']?.includes('application/json'),
    { timeout: 90000 },
  );

  const facilityConfigResponse = page.waitForResponse(
    (response) =>
      response.url().includes('/config/facility-config') &&
      response.status() === 200 &&
      response.headers()['content-type']?.includes('application/json'),
    { timeout: 90000 },
  );

  await app.login(config.user, config.organization);
  await AuthenticationService.instance.interceptToken(tokenResponse);
  await AppConfigurationService.instance.interceptFacilityConfig(facilityConfigResponse);

  await page.context().storageState({ path: CHROMIUM_STORAGE_STATE });
  await browser.close();
}

async function cacheStorageState_for_firefox(config: ITestConfigurationService) {
  const browser: Browser = await firefox.launch();
  const context = await browser.newContext();

  const page: Page = await context.newPage();
  const app = new Login(page, config);

  await app.goTo();

  // Only intercept successful token and facility-config responses with content
  const tokenResponse = page.waitForResponse(
    (response) =>
      response.url().includes('/oauth/token') &&
      response.status() === 200 &&
      response.headers()['content-type']?.includes('application/json'),
    { timeout: 90000 },
  );

  const facilityConfigResponse = page.waitForResponse(
    (response) =>
      response.url().includes('/config/facility-config') &&
      response.status() === 200 &&
      response.headers()['content-type']?.includes('application/json'),
    { timeout: 90000 },
  );

  await app.login(config.user, config.organization);
  await AuthenticationService.instance.interceptToken(tokenResponse);
  await AppConfigurationService.instance.interceptFacilityConfig(facilityConfigResponse);

  await page.context().storageState({ path: FIREFOX_STORAGE_STATE });
  await browser.close();
}
