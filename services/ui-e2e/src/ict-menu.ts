export const IctMenu = {
  OperationsVisibility: {
    title: 'Operations Visibility',
    id: 'operations-visibility',
    OutboundOverview: {
      title: 'Outbound Overview',
      id: 'outbound-overview',
      url: '/ict-outbound-overview',
      path: ['operations-visibility'],
    },
    InboundOverview: {
      title: 'Inbound Overview',
      id: 'inbound-overview',
      type: 'navItem',
      url: '/ict-inbound-overview',
      path: ['operations-visibility'],
    },
    Inventory: {
      title: 'Inventory',
      id: 'inventory',
      path: ['operations-visibility'],
      InventoryOverview: {
        title: 'Inventory Overview',
        id: 'inventory-overview',
        url: '/ict-inventory-overview',
        path: ['operations-visibility', 'inventory'],
      },
      InventoryList: {
        title: 'Inventory LIst',
        id: 'inventory-list',
        url: '/ict-inventory-list',
        path: ['operations-visibility', 'inventory'],
      },
      ContainerList: {
        title: 'Container List',
        id: 'container-list',
        url: '/ict-container-list',
        path: ['operations-visibility', 'inventory'],
      },
      ReplenishmentDetails: {
        title: 'Replenishment Details',
        id: 'replenishment-details',
        url: '/ict-replenishment-details',
        path: ['operations-visibility', 'inventory'],
      },
    },
    ReplenishmentDetails: {
      title: 'Replenishment Details',
      id: 'replenishment-details',
      url: '/ict-replenishment-details',
      path: ['operations-visibility', 'inventory'],
    },
    Workstation: {
      title: 'Workstation',
      id: 'workstation',
      WorkstationOverview: {
        title: 'Workstation Overview',
        id: 'workstation-overview-dashboard',
        url: '/ict-workstation-overview',
        path: ['operations-visibility', 'workstation'],
      },
    },
    PickingBufferAreaDetails: {
      title: 'Picking Buffer Area Details',
      id: 'picking-buffer-area-details',
      type: 'navItem',
      url: '/ict-picking-buffer-area-details',
      path: ['operations-visibility'],
    },
  },
  ProcessFlowVisualization: {
    title: 'Process Flow Visualization',
    id: 'process-flow-visualization',
    FacilityProcessFlow: {
      title: 'Facility Process Flow',
      id: 'facility-process-flow',
      url: '/ict-facility-process-flow',
      path: ['process-flow-visualization'],
    },
  },
  ApplicationHealth: {
    title: 'Application Health',
    id: 'application-health',
    CuratedData: {
      title: 'Curated Data',
      id: 'curated-data',
      url: '/ict-outbound-overview',
      path: ['application-health'],
    },
  },
  ScenarioModeling: {
    title: 'Scenario Modeling',
    id: 'scenario-modeling',
    Simulation: {
      title: 'Simulation',
      id: 'simulation',
      url: '/ict-simulation',
      path: ['scenario-modeling'],
    },
  },
  AdvancedOrchestration: {
    title: 'Advanced Orchestration',
    id: 'advanced-orchestration',
    InventoryForecast: {
      title: 'Inventory Forecast',
      id: 'inventory-forecast',
      url: '/ict-inventory-forecast',
      path: ['advanced-orchestration'],
    },
    FileUpload: {
      title: 'File Upload',
      id: 'file-upload',
      url: '/ict-file-upload',
      path: ['advanced-orchestration'],
    },
  },
  SystemAvailability: {
    title: 'System Availability',
    id: 'system-availability',
    FaultTracking: {
      title: 'Availability',
      id: 'fault-tracking',
      url: '/ict-fault-tracking',
      path: ['system-availability'],
    },
  },
  DataExplorer: {
    title: 'Data Explorer',
    id: 'ict-data-explorer',
    url: '/ict-data-explorer',
    path: [],
  },
};
