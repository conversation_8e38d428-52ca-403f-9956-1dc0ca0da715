import { KpiChartWidgetId, KpiWidgetId } from '@ui-adk/components/widgets/widget-id';
import { KpiWidgetDefinition, WidgetDefinition } from '@ui-adk/components/widgets/widget-definition';
import { WidgetType } from '@ui-adk/components/widgets/widget-types';
import { DashboardDefinition, WidgetId, WidgetLibrary } from '@ui-adk/types/dashboard';

/**
 *  define the kpi's for the inventory overview page.
 */
const kpiWidgets: Map<KpiWidgetId, WidgetDefinition> = new Map<KpiWidgetId, KpiWidgetDefinition>();
kpiWidgets.set('kpi-orders-fulfillment-outstanding', {
  type: 'kpi',
  testId: 'kpi-orders-fulfillment-outstanding',
  title: 'Fulfillment Orders Outstanding',
});
kpiWidgets.set('kpi-customer-orders-projected-fulfillment', {
  type: 'kpi',
  testId: 'kpi-customer-orders-projected-fulfillment',
  title: 'Projected Customer Order Fulfillment',
});
kpiWidgets.set('kpi-orders-pick-cycle-counts', {
  type: 'kpi',
  testId: 'kpi-orders-pick-cycle-counts',
  title: 'Cycle Counts',
});
kpiWidgets.set('kpi-inventory-storage-utilization', {
  type: 'kpi',
  testId: 'kpi-inventory-storage-utilization',
  title: 'Storage Utilization',
});

const kpiChartWidgets: Map<KpiChartWidgetId, WidgetDefinition> = new Map<KpiChartWidgetId, WidgetDefinition>();
kpiChartWidgets.set('kpi-chart-inventory-wms-stock-distribution-at-percentage', {
  type: 'kpi-chart',
  testId: 'kpi-chart-inventory-wms-stock-distribution-at-percentage',
  title: 'At Inventory',
});
kpiChartWidgets.set('kpi-chart-inventory-wms-stock-distribution-no-percentage', {
  type: 'kpi-chart',
  testId: 'kpi-chart-inventory-wms-stock-distribution-no-percentage',
  title: 'No Inventory',
});
kpiChartWidgets.set('kpi-chart-inventory-wms-stock-distribution-over-percentage', {
  type: 'kpi-chart',
  testId: 'kpi-chart-inventory-wms-stock-distribution-over-percentage',
  title: 'Over Inventory',
});
kpiChartWidgets.set('kpi-chart-inventory-wms-stock-distribution-under-percentage', {
  type: 'kpi-chart',
  testId: 'kpi-chart-inventory-wms-stock-distribution-under-percentage',
  title: 'Under Inventory',
});

const widgets: WidgetLibrary = new Map<WidgetType, Map<WidgetId, WidgetDefinition>>();
widgets.set('kpi', kpiWidgets);
widgets.set('kpi-chart', kpiChartWidgets);

export const ict_inventory_overview: DashboardDefinition = {
  id: 'ict-inventory-overview',
  url: 'ict-inventory-overview',
  title: 'Inventory Overview',
  widgets,
  apiEndpoints: [
    'inventory/storage/utilization',
    'orders/pick/cycle-count',
    'orders/fulfillment-outstanding',
    'orders/customer/fulfillment',
    'inventory/wms/stock/distribution/under/percentage/series',
    'inventory/wms/stock/distribution/over/percentage/series',
    'inventory/wms/stock/distribution/no/percentage/series',
    'inventory/wms/stock/distribution/at/percentage/series',
  ],
};
