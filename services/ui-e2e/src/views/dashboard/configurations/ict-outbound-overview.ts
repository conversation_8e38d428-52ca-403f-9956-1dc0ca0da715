import { KpiWidgetId } from '@ui-adk/components/widgets/widget-id';
import {
  KpiWidgetDefinition,
  OutboundOverviewAreasWidgetDefinition,
  WidgetDefinition,
} from '@ui-adk/components/widgets/widget-definition';
import { WidgetType } from '@ui-adk/components/widgets/widget-types';
import { DashboardDefinition, WidgetLibrary } from '@ui-adk/types/dashboard';

/**
 *  define the kpi's for the outbound overview page.
 */
const kpiWidgets: Map<KpiWidgetId, WidgetDefinition> = new Map<KpiWidgetId, KpiWidgetDefinition>();
kpiWidgets.set('kpi-orders-shipped', { type: 'kpi', testId: 'kpi-orders-shipped', title: 'Customer Orders Shipped' });
kpiWidgets.set('kpi-orders-progress', { type: 'kpi', testId: 'kpi-orders-progress', title: 'Order Progress' });
kpiWidgets.set('kpi-orders-lines-progress', { type: 'kpi', testId: 'kpi-orders-lines-progress', title: 'Order Line Progress' });
kpiWidgets.set('kpi-operators-active', { type: 'kpi', testId: 'kpi-operators-active', title: 'Active Operators' });
kpiWidgets.set('kpi-orders-lines-progress', { type: 'kpi', testId: 'kpi-orders-lines-progress', title: 'Order Line Progress' });
kpiWidgets.set('kpi-orders-throughput-rate', { type: 'kpi', testId: 'kpi-orders-throughput-rate', title: 'Throughput Rate' });
kpiWidgets.set('kpi-orders-customer-line-throughput-rate', {
  type: 'kpi',
  testId: 'kpi-orders-customer-line-throughput-rate',
  title: 'Customer Line Throughput Rate',
});
kpiWidgets.set('kpi-orders-customer-line-progress', {
  type: 'kpi',
  testId: 'kpi-orders-customer-line-progress',
  title: 'Customer Line Progress',
});
kpiWidgets.set('kpi-orders-customer-cycle-time', {
  type: 'kpi',
  testId: 'kpi-orders-customer-cycle-time',
  title: 'Customer Cycle Time',
});

const areasWidget = new Map<'outbound-overview-areas', OutboundOverviewAreasWidgetDefinition>();
areasWidget.set('outbound-overview-areas', {
  type: 'outbound-overview-areas',
  testId: 'outbound-overview-areas',
  areas: ['Shipping Area', 'Picking Area'],
});

const widgets: WidgetLibrary = new Map<WidgetType, Map<KpiWidgetId, WidgetDefinition>>();
widgets.set('kpi', kpiWidgets);
widgets.set('outbound-overview-areas', areasWidget);

export const ict_outbound_overview: DashboardDefinition = {
  id: 'ict-outbound-overview',
  url: 'ict-outbound-overview',
  title: 'Outbound Overview',
  widgets,
  apiEndpoints: [
    'orders/customer/shipped',
    'orders/customer/line/progress',
    'orders/customer/throughput',
    'orders/customer/cycletime',
    'operators/active',
    'orders/customer/progress',
    'orders/customer/line/throughput/series',
    'orders/customer/line/progress/series',
  ],
};
