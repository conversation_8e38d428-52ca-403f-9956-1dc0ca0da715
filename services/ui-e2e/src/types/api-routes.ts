export type ApiReturnCode = 200 | 201 | 202 | 204 | 304 | 400 | 401 | 403 | 404 | 409 | 422 | 429 | 500 | 501 | 502;

export const ApiRoutes = [
  'config/settings',
  'orders/customer/cycletime',
  'orders/customer/shipped',
  'orders/facility/shipped',
  'orders/customer/line/progress',
  'orders/facility/line/progress',
  'orders/facility/line/progress/series',
  'orders/customer/progress',
  'orders/facility/progress',
  'orders/facility/completion',
  'orders/customer/throughput',
  'orders/facility/cycletime',
  'orders/facility/throughput',
  'orders/customer/line/progress/series',
  'orders/customer/line/throughput/series',
  'orders/facility/line/throughput/series',
  'operators/active',
  'workstation/metrics/operators',
  'workstation/metrics/summary',
  'workstation/list',
  'workstation/series',
  'inventory/skus/list',
  'inventory/process-flow/areas',
  'inventory/containers/list',
  'inventory/advices/list',
  'inventory/advices/outstanding',
  'inventory/advices/finished',
  'inventory/advices/in-progress',
  'inventory/advices/cycle-time',
  'inventory/advices/finished',
  'inventory/process-flow/areas',
  'inventory/process-flow/details/node',
  'inventory/process-flow/area/workstations',
  'inventory/forecast',
  'inventory/forecast/list',
  'data-explorer/search',
  'data-explorer/results',
  'inventory/storage/utilization',
  'orders/pick/cycle-count',
  'orders/fulfillment-outstanding',
  'orders/customer/fulfillment',
  'orders/facility/outstanding',
  'inventory/wms/stock/distribution/under/percentage/series',
  'inventory/wms/stock/distribution/over/percentage/series',
  'inventory/wms/stock/distribution/no/percentage/series',
  'inventory/wms/stock/distribution/at/percentage/series',
  'inventory/replenishment/details',
  'inventory/replenishment/task-type-series',
  'workstation/metrics/health',
  'workstation/metrics/operators',
  'workstation/metrics/performance',
  'workstation/metrics/summary',
  'workstation/list',
  'equipment/faults/grouped/count/series',

  'config/curated-tables/list',
  'config/v2/curated-data',
] as const;

export type ApiRoute = (typeof ApiRoutes)[number];

export type ApiEndpoint = {
  path: ApiRoute;
  queryParams?: string[];
};
