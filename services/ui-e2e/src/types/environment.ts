export type EnvironmentName = (typeof Environments)[number];

export const IctUrls = [
  'http://localhost:4200',
  'https://dev.ict.dematic.dev',
  'https://stage.ict.dematic.dev',
  'https://ict.dematic.cloud',
] as const;
export type IctUrl = (typeof IctUrls)[number];

export const IctApiEndpoints = [
  'http://localhost:8000',
  'https://api.dev.ict.dematic.dev',
  'https://api.stage.ict.dematic.dev',
  'https://api.ict.dematic.cloud',
] as const;
export type IctApi = (typeof IctApiEndpoints)[number];

export const IctMockApiEndpoints = [
  'http://localhost:8000/',
  'https://api.dev.ict.dematic.dev/mock/',
  'https://api.stage.ict.dematic.dev/mock/',
  'https://api.ict.dematic.cloud/mock/',
] as const;
export type IctMockApi = (typeof IctMockApiEndpoints)[number];

export type EnvironmentConfig = {
  name: EnvironmentName;
  baseUrl: IctUrl;
  apiUrl: IctApi;
  mockApiUrl: IctMockApi;
};

export const Local: EnvironmentConfig = {
  name: 'local',
  baseUrl: 'http://localhost:4200',
  apiUrl: 'http://localhost:8000',
  mockApiUrl: 'http://localhost:8000/',
} as const;

export const LocalDev: EnvironmentConfig = {
  name: 'local-dev',
  baseUrl: 'http://localhost:4200',
  apiUrl: 'https://api.dev.ict.dematic.dev',
  mockApiUrl: 'https://api.dev.ict.dematic.dev/mock/',
} as const;

export const Dev: EnvironmentConfig = {
  name: 'dev',
  baseUrl: 'https://dev.ict.dematic.dev',
  apiUrl: 'https://api.dev.ict.dematic.dev',
  mockApiUrl: 'https://api.dev.ict.dematic.dev/mock/',
} as const;

export const Stage: EnvironmentConfig = {
  name: 'stage',
  baseUrl: 'https://stage.ict.dematic.dev',
  apiUrl: 'https://api.stage.ict.dematic.dev',
  mockApiUrl: 'https://api.stage.ict.dematic.dev/mock/',
} as const;

export const Prod: EnvironmentConfig = {
  name: 'prod',
  baseUrl: 'https://ict.dematic.cloud',
  apiUrl: 'https://api.ict.dematic.cloud',
  mockApiUrl: 'https://api.ict.dematic.cloud/mock/',
} as const;

export const EnvironmentConfigs = [Local, LocalDev, Dev, Stage, Prod];

export const Environments = ['local-dev', 'preview', 'local', 'dev', 'stage', 'prod'] as const;

export type Environment = typeof Local | typeof LocalDev | typeof Dev | typeof Stage | typeof Prod;

export function getEnv(name: EnvironmentName) {
  const found = EnvironmentConfigs.find((env) => env.name === name);
  if (found) return found;

  throw new Error(`Unknown env: ${name}!`);
}

export function setPreviewEnv(url: string): EnvironmentConfig {
  return {
    name: 'preview',
    baseUrl: url as IctUrl,
    apiUrl: 'https://api.ict.dematic.cloud',
    mockApiUrl: 'https://api.dev.ict.dematic.dev/mock/',
  } as const;
}
