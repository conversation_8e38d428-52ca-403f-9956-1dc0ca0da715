import 'reflect-metadata';
import { User, Tenant, DefaultTenant } from '@ui-adk/types';
import { Environment, getEnv, EnvironmentName, setPreviewEnv } from '@ui-adk/types/environment';
import { Service } from 'typedi';
import * as dotenv from 'dotenv';
import path from 'path';
import { TestEnvironmentSchema } from './types';

const CONFIGURATION_FILE = path.join(process.cwd(), '.env');

export interface ITestConfigurationService {
  env: Environment;
  user: User;
  organization: Tenant;
  useMockApi: boolean;
}

@Service()
export class TestConfigurationService implements ITestConfigurationService {
  constructor() {
    dotenv.config({ path: CONFIGURATION_FILE });
    const res = TestEnvironmentSchema.safeParse(process.env);

    if (!res.success) {
      for (let index = 0; index < res.error.errors.length; index++) {
        const er = res.error.errors[index];

        console.error(`[${er.path}  ${er.message}]`);
      }

      throw new Error(`Test Configuration file ${CONFIGURATION_FILE} is not valid!  See errors Above:`);
    }

    // cache the requested environment...
    const requestEnv: EnvironmentName = res.data.ICT_ENV as EnvironmentName;

    // if this is a CI preview env we need to construct the env with the preview url.
    if (requestEnv === 'preview') {
      this.env = setPreviewEnv(process.env.ICT_BASE_URL);
    } else {
      this.env = getEnv(res.data.ICT_ENV);
    }
    this.user = {
      email: res.data.ICT_USER_EMAIL,
      password: res.data.ICT_USER_PASSWORD,
    };
    this.blockedUser = {
      email: res.data.BLOCKED_USER_EMAIL,
      password: res.data.BLOCKED_USER_PASSWORD,
    };
    this.unorganizedUser = {
      email: res.data.UNORGANIZED_USER_EMAIL,
      password: res.data.UNORGANIZED_USER_PASSWORD,
    };
    this.unauthorizedUser = {
      email: res.data.UNAUTHORIZED_USER_EMAIL,
      password: res.data.UNORGANIZED_USER_PASSWORD,
    };

    this.organization = res.data.ICT_ORGANIZATION ? (res.data.ICT_ORGANIZATION as Tenant) : DefaultTenant;

    this.useMockApi = res.data.USE_MOCK_API?.toLowerCase() !== 'false';
  }

  env: Environment;
  user: User;
  blockedUser: User;
  unorganizedUser: User;
  unauthorizedUser: User;
  organization: Tenant;
  useMockApi: boolean;
}
