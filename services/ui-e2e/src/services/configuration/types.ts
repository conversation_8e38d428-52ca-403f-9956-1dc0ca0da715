import { z } from 'zod';
import { Environments } from '../../types/environment';

export const TestEnvironmentSchema = z.object({
  ICT_USER_EMAIL: z.string(),
  ICT_USER_PASSWORD: z.string(),
  BLOCKED_USER_EMAIL: z.string().optional(),
  BLOCKED_USER_PASSWORD: z.string().optional(),
  UNORGANIZED_USER_EMAIL: z.string().optional(),
  UNORGANIZED_USER_PASSWORD: z.string().optional(),
  UNAUTHORIZED_USER_EMAIL: z.string().optional(),
  UNAUTHORIZED_USER_PASSWORD: z.string().optional(),
  ICT_ENV: z.enum(Environments),
  ICT_BASE_URL: z.string().optional(),
  ICT_API_URL: z.string().optional(),
  ICT_MOCK_API_URL: z.string().optional(),
  ICT_ORGANIZATION: z.string().optional(),
  USE_MOCK_API: z.string().optional(),
});

export type TestEnvironmentConfiguration = z.infer<typeof TestEnvironmentSchema>;
