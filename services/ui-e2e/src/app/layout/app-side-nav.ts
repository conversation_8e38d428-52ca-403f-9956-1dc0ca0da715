import { Page } from '@playwright/test';
import { AbstractElementBase } from '@ui-adk/components/abstract-element-base';
import { isMenuChildItem, MenuChildItem, MenuGroupItem, MenuItem } from '@ui-adk/types/menu-item';
import { AppSideNavMenuItem } from './app-side-nav-menu-item';
import { AppSideNavMenuGroup } from './app-side-nav-menu-group';

/**
 * Application side nav menu wrapper.
 */
export class AppSideNav extends AbstractElementBase {
  /**
   *
   */
  constructor(page: Page) {
    super(page, page.getByTestId('app-side-nav'));
  }

  public async itemExists(item: MenuItem) {
    await this.waitForLocator(this.locator.locator('.cds--side-nav__items').first(), 'visible');
    if (isMenuChildItem(item) === true) {
      /**
       * Expand the menu group...
       */
      const child = item as MenuChildItem;
      for await (const pathItem of child.path) {
        const menuGroup = new AppSideNavMenuGroup(this.page, this.locator.getByTestId(pathItem));
        await menuGroup.expand();
      }

      const menuItem = new AppSideNavMenuItem(this.page, this.locator.getByTestId(child.id));
      return await menuItem.isVisible();
    }

    const groupItem = item as MenuGroupItem;
    const menuGroup = new AppSideNavMenuGroup(this.page, this.locator.getByTestId(groupItem.id));
    return await menuGroup.isVisible();
  }

  /**
   *
   */
  public async selectItem(item: MenuItem) {
    if (isMenuChildItem(item) === true) {
      /**
       * Expand the menu group...
       */
      const child = item as MenuChildItem;
      for await (const pathItem of child.path) {
        const menuGroup = new AppSideNavMenuGroup(this.page, this.locator.getByTestId(pathItem));
        await menuGroup.expand();
      }

      /**
       * Selection the menu item...
       */
      const menuItem = new AppSideNavMenuItem(this.page, this.locator.getByTestId(child.id));
      await menuItem.select();
    }
  }

  public async itemHasIcon(item: MenuGroupItem) {
    const groupItem = item as MenuGroupItem;
    const menuGroup = new AppSideNavMenuGroup(this.page, this.locator.getByTestId(groupItem.id));
    return menuGroup.isMenuIconVisible;
  }
}
