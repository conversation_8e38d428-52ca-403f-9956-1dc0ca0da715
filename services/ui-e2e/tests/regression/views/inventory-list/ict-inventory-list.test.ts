import { expect } from '@playwright/test';
import { TestGroup } from '@ui-tests/test-groups';
import { test } from '@ui-tests/fixtures/regression-fixture';
import { HeaderValidationTests } from './data/test-data';
import { getRandomItem } from '@ui-tests/utils/utils';

test.describe(`ict-inventory-list ${TestGroup.Regression}`, async () => {
  HeaderValidationTests.forEach(({ header, testId }) => {
    test(`[@C${testId}] - Verify table contains ${header} column.`, async ({ inventoryList }) => {
      const columns = await inventoryList.datagrid.table.getColumnHeaders();
      expect(columns).toContain(header);
    });
  });
  test('[@C3360347] - Verify that table is sorted ascending by Days on Hand column by default.', async ({ inventoryList }) => {
    const sortDirection = await inventoryList.datagrid.table.getColumnSortDirection('Days On Hand');
    expect(sortDirection).toEqual('ascending');
  });
  test.use({ useMockApi: false });
  test.fixme('[@C3428196] - Verify that clicking Refresh button gets the latest table data.', async ({ inventoryList }) => {
    await inventoryList.datagrid.waitForLoad();
    const beforeRefresh = await inventoryList.getLastUpdatedTimeStamp();
    await inventoryList.datagrid.refresh();
    const afterRefresh = await inventoryList.getLastUpdatedTimeStamp();
    expect(beforeRefresh.getTime()).toBeLessThan(afterRefresh.getTime());
  });
  test('[@C3589763] - Verify inventory List has an Export button.', async ({ inventoryList }) => {
    expect(await inventoryList.datagrid.exportButton.isVisible()).toBeTruthy();
  });
  test('[@C3589970] - Verify the user can select/click a SKU link in the Inventory List.', async ({
    inventoryList,
    containerList,
  }) => {
    const inventoryTable = inventoryList.datagrid.table;
    const skuList = await inventoryTable.getColumn(await inventoryTable.getColumnIndex('SKU'));

    expect(await inventoryList.clickSKULink(getRandomItem<string>(skuList)));
    expect(await containerList.getUrl()).toContain('ict-container-list');
  });
});
