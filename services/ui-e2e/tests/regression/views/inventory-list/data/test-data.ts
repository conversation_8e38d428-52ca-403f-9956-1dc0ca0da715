export type InventoryListTableHeaderValidationTest = {
  header: string;
  /**
   * Test rail test id.
   */
  testId: number;
};

export const HeaderValidationTests: InventoryListTableHeaderValidationTest[] = [
  { header: 'SKU', testId: 3360344 },
  { header: 'Qty Available', testId: 3550322 },
  { header: 'Qty Allocated', testId: 3550323 },
  { header: 'Max Containers', testId: 3550324 },
  { header: 'SKU Positions', testId: 3550325 },
  { header: 'Container Overage', testId: 3550326 },
  { header: 'Days On Hand', testId: 3360346 },
  { header: 'Avg Daily Qty', testId: 3549815 },
  { header: 'Avg Daily Orders', testId: 3550317 },
  { header: 'Latest Activity', testId: 3550327 },
  { header: 'Latest Cycle Count', testId: 3550328 },
  { header: 'Description', testId: 3360345 },
  { header: 'Target Multiplicity', testId: 3550329 },
  { header: 'Velocity Classification', testId: 3550330 },
];
