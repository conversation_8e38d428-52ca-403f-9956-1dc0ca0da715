import { expect } from '@playwright/test';
import { TestGroup } from '@ui-tests/test-groups';
import { test } from '@ui-tests/fixtures/regression-fixture';
import { KpiWidget } from '@ui-adk/components/widgets';
import { KpiChartWidgetDefinition, KpiWidgetDefinition } from '@ui-adk/components/widgets/widget-definition';
import { IctMenu } from '@ui-adk/ict-menu';
import { KpiChartWidget } from '@ui-adk/components/widgets/kpi-chart-widget';
import { ict_inventory_overview } from '@ui-adk//views/dashboard/configurations/ict-inventory-overview';

const dashboardDefinition = ict_inventory_overview;

test.describe(`ict-inventory-overview - ${TestGroup.Regression}`, async () => {
  test.use({ dashboardId: 'inventory-overview' });

  test(`Verify dashboard title:`, async ({ dashboard }) => {
    expect(await dashboard.getTitle()).toEqual(dashboardDefinition.title);
  });

  test(`Verify dashboard route:`, async ({ dashboard }) => {
    expect(await dashboard.getUrl()).toContain(dashboardDefinition.url);
  });

  test(`Verify user can navigate via menu`, async ({ controlTower, dashboard }) => {
    expect(await controlTower.sideMenu.isVisible()).toBeTruthy();
    await controlTower.sideMenu.selectItem(IctMenu.OperationsVisibility.Inventory.InventoryOverview);
    expect(await dashboard.getTitle()).toEqual(dashboardDefinition.title);
  });

  test(`Verify dashboard KPI widgets: `, async ({ dashboard }) => {
    const kpiCardIds = dashboardDefinition.widgets.get('kpi').keys();
    for await (const kpi of kpiCardIds) {
      const kpiDef = dashboardDefinition.widgets.get('kpi').get(kpi) as KpiWidgetDefinition;
      const kpiWidget = await dashboard.getWidget<KpiWidget>(kpiDef);
      expect(await kpiWidget.isVisible(), `Check KPI widget '${kpiDef.title}' is visible`).toBeTruthy();
      expect(await kpiWidget.header.getTitle()).toEqual(kpiDef.title);
    }
  });

  test(`Verify dashboard KPI Chart widgets: `, async ({ dashboard }) => {
    const kpiChartIds = dashboardDefinition.widgets.get('kpi-chart').keys();
    for await (const kpiChart of kpiChartIds) {
      const kpiChartDef = dashboardDefinition.widgets.get('kpi-chart').get(kpiChart) as KpiChartWidgetDefinition;
      const kpiWidget = await dashboard.getWidget<KpiChartWidget>(kpiChartDef);
      expect(await kpiWidget.isVisible(), `Check KPI Chart widget '${kpiChartDef.title}' is visible`).toBeTruthy();
      expect(await kpiWidget.header.getTitle()).toEqual(kpiChartDef.title);
    }
  });
});
