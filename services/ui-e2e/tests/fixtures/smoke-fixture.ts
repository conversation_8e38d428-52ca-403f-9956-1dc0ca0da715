import { APIRequestContext, request, test as base } from '@playwright/test';
import { Login } from '@ui-adk/views';
import { ControlTower } from '@ui-adk/app';
import { TestConfigurationService } from '@ui-adk/services/configuration/test-configuration-service';
import Container from 'typedi';
import { User } from '@ui-adk/types/user';
import { Tenant } from '@ui-adk/types';
import { Debug } from '@ui-adk/views/debug-view/debug-view';
import { AuthenticationService } from '@ui-adk/services/authentication';

export type TestOptions = {
  /**
   * controls the default user information that will be used for a test.
   */
  user: User;

  /**
   * controls the default tenant to be used for a test.
   */
  tenant: Tenant;

  /**
   * controls if we will use the MOCK api for testing.
   */
  useMockApi: boolean;

  /**
   * Represents the application entry point.
   */
  controlTower: ControlTower;

  /**
   * Represents the login page.
   */
  logIn: Login;

  /**
   * Represents the debug page.
   */
  debug: Debug;

  /**
   * Represents the APIRequestContext.
   */
  api: APIRequestContext;
};

export const test = base.extend<TestOptions>({
  user: [Container.get(TestConfigurationService).user, { option: true }],
  tenant: [Container.get(TestConfigurationService).organization, { option: true }],
  useMockApi: [Container.get(TestConfigurationService).useMockApi, { option: true }],

  controlTower: async ({ page }, use) => {
    const controlTower = new ControlTower(page, Container.get(TestConfigurationService));
    await use(controlTower);
  },

  debug: async ({ page, useMockApi }, use) => {
    const view = new Debug(page, Container.get(TestConfigurationService));
    await view.goto();
    if (useMockApi) {
      await view.allMock();
    }
    await use(view);
  },

  logIn: async ({ page }, use) => {
    const login = new Login(page, Container.get(TestConfigurationService));
    await use(login);
  },

  api: async ({ useMockApi }, use) => {
    const token = await AuthenticationService.instance.getCachedToken();
    const baseURL = useMockApi
      ? Container.get(TestConfigurationService).env.mockApiUrl
      : Container.get(TestConfigurationService).env.apiUrl;
    const requestContext = await request.newContext({
      baseURL: baseURL,
      extraHTTPHeaders: {
        Authorization: `Bearer ${token.access_token}`,
        'Content-Type': 'application/json',
      },
      ignoreHTTPSErrors: true,
    });
    await use(requestContext);
    await requestContext.dispose();
  },
});
