enum TestGroups {
  Smoke = 'smoke',
  Regression = 'regression',
  Login = 'login',
  Dashboard = 'dashboard',
  Charts = 'charts',
  Visual = 'visual',
}

enum SuitePrefix {
  Suite = '@suite:',
}

function withPrefix(prefix: string, values: Record<string, string>) {
  const result: Record<string, string> = {};
  for (const key in values) {
    if (Object.prototype.hasOwnProperty.call(values, key)) {
      result[key] = `${prefix}${values[key]}`;
    }
  }
  return result as typeof values;
}

export const TestGroup = withPrefix(SuitePrefix.Suite, TestGroups);
