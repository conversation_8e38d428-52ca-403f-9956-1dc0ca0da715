# Creating New Environments

This guide explains how to create a new environment for testing or development purposes.

## Quick Start

1. **Run the script:**

   ```bash
   ./scripts/create-environment.sh stage my_test mytest MyTest
   ```

2. **Create the project:**

   ```bash
   cd environments/dematicnonprod/my_test/ict-np-us1-mytest/project
   terragrunt init -reconfigure
   terragrunt plan
   terragrunt apply
   ```

3. **Create the resources:**
   ```bash
   cd ../resources
   terragrunt init -reconfigure
   terragrunt plan
   terragrunt apply
   ```

## What This Creates

- **New GCP Project**: `ict-np-us1-mytest-abc123` (with random suffix)
- **All Infrastructure**: Cloud Run services, databases, load balancers, etc.
- **Custom IP Ranges**: To avoid conflicts with existing environments
- **Proper Configuration**: All files updated with new names

## Source Environment Choices

| Environment | Description             | Use Case                                           |
| ----------- | ----------------------- | -------------------------------------------------- |
| `dev`       | Development environment | Has simulation API keys, dev-specific configs      |
| `stage`     | Staging environment     | Production-like, cleaner for testing (recommended) |
| `prod`      | Production environment  | Use with extreme caution!                          |

## Script Parameters

```bash
./scripts/create-environment.sh <source_env> <new_env_name> <instance_name> <instance_friendly_name>
```

### Examples

```bash
# Create a test environment from stage
./scripts/create-environment.sh stage ben_test bentest BenTest

# Create a development environment from dev
./scripts/create-environment.sh dev my_dev mydev MyDev

# Create a feature environment from stage
./scripts/create-environment.sh stage feature_a featurea FeatureA
```

## Important Notes

- **Always create PROJECT before RESOURCES** (the script will remind you)
- **Use `-reconfigure` flag** for new environments
- **Check IP ranges** if you get conflicts
- **Clean up** when done: `terragrunt destroy` in both project and resources directories

## Troubleshooting

### "Backend configuration changed" error

- Use `terragrunt init -reconfigure` for new environments

### "Project outputs not found" error

- Make sure you created the project before trying to create resources

### IP range conflicts

- Edit the IP ranges in the resources `terragrunt.hcl` file
- Look for the `global_default_ip_ranges` section
